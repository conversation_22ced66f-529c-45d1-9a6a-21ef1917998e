<template>
  <div>
    <div
      v-if="show"
      :class="['fixed right-0 z-1000 large-card', isAnimating ? 'sliding-out' : '']"
      :style="{ bottom: currentBottom + 'px' }"
    >
      <div
        class="w-[164px] pl-[8px] h-[58px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-tl-[100px] rounded-br-none rounded-tr-none rounded-bl-[100px] box-border flex items-center" 
        @click="pay"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
      >
        <img
          class="block w-[40px] h-[40px]"
          alt=""
          src="@/pages/submit-question/ai/img/uvky2u.png"
        >
        <div class="ml-[4px]">
          <div class="font-bold text-[13px] text-[#FFFFFF]">
            转接高级律师
          </div>
          <div class="mt-[2px] flex items-center">
            <div class="text-[16px] text-[#FFFFFF]">
              <span class="text-[12px]">¥</span>{{ amountFilter(serverInfo.servicePrice) }}
            </div>
            <div class="text-[12px] text-[#FFFFFF] [text-decoration-line:line-through] ml-[2px] opacity-50">
              ¥{{ amountFilter(serverInfo.originalPrice) }}
            </div>
          </div>
        </div>
        <img
          class="block w-[14px] h-[14px] ml-[10px]"
          alt=""
          src="@/pages/submit-question/ai/img/icon1.png"
          @click.stop="close"
        >
      </div>
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import AiPayMixin from "@/pages/submit-question/ai/AiPayMixin.js";
import { PAGE_MARK_ENUM, POSITION_ENUM } from "@/libs/getPageInfo";

export default {
  name: "LawyerImPayCard",
  components: {
    USafeBottom,
  },
  mixins: [AiPayMixin],
  data() {
    return {
      show: true, 
      isAnimating: false,
      pageMark: PAGE_MARK_ENUM.SUBMIT_QUESTION_AI,
      position: POSITION_ENUM.BOTTOM_CONSULT_CARD,
      buryPointPath: 5059,
      buryPointCode: "LAW_APPLET_AI_ASK_PAGE_TOP_BUTTON_CLICK",
      // 拖动相关状态
      isDragging: false,
      startY: 0,
      startBottom: 77,
      currentBottom: 77,
      cardHeight: 58,
      screenHeight: 0,
      safeAreaBottom: 0,
    };
  },
  mounted() {
    // 获取系统信息
    uni.getSystemInfo({
      success: (res) => {
        this.screenHeight = res.windowHeight;
        this.safeAreaBottom = res.safeAreaInsets ? res.safeAreaInsets.bottom : 0;
      }
    });
  },
  methods: {
    close() {
      this.isAnimating = true;
      setTimeout(() => {
        this.show = false;
        this.isAnimating = false;
      }, 300); // 动画时长
    },
    showLargeCard() {
      this.show = true;
    },
    // 触摸开始
    onTouchStart(e) {
      this.isDragging = true;
      this.startY = e.touches[0].clientY;
      this.startBottom = this.currentBottom;
    },
    // 触摸移动
    onTouchMove(e) {
      if (!this.isDragging) return;
      e.preventDefault();
      
      const currentY = e.touches[0].clientY;
      const deltaY = this.startY - currentY; // 向上为正值
      const newBottom = this.startBottom + deltaY;
      
      // 计算边界限制
      const minBottom = this.safeAreaBottom + 10; // 最小距离底部10px
      const maxBottom = this.screenHeight - this.cardHeight - 50; // 最大距离底部（避免超出顶部）
      
      // 限制拖动范围
      this.currentBottom = Math.max(minBottom, Math.min(maxBottom, newBottom));
    },
    // 触摸结束
    onTouchEnd() {
      this.isDragging = false;
    },
  },
};
</script>

<style scoped>
.small-card {
  animation: slideIn 0.3s ease forwards;
}

.large-card {
  animation: slideIn 0.3s ease forwards;
}

.sliding-out {
  animation: slideOut 0.3s ease forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
</style>
