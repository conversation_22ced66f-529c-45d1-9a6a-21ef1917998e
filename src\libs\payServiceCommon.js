import { caseSourceV2GetCaseOrZx } from "@/api/special";
import { getParalegalDataId, saveParalegalData } from "@/libs/paralegalData";
import Store from "@/store";
import { toConfirmOrder } from "@/libs/tools";

/**
 * 如果有留资信息时下公共订单，则优先使用留资信息里面的信息
 * 如果没有，则直接走服务器下单
 * 与下面的方法的不同点是不会直接使用留资信息里面的位置信息
 */
export async function payServiceCommonInfo({
  serviceCode,
  params = {
    info: "用户未输入问题描述",
    typeLabel: "综合咨询",
    typeValue: 26,
    type: 3
  },
  paySuccessCallback,
  productPriceCoefficientKey
}) {
  try {
    const res = await caseSourceV2GetCaseOrZx();

    const data = res.data || {};

    let query = {
      serviceCode,
      paySuccessCallback,
      productPriceCoefficientKey
    };

    /** 0 无 1 案源 2 问答 */
    const type = Number(data.type);

    if (type === 0) {
      await saveParalegalData(params);
    } else {
      await saveParalegalData({
        ...params,
        info: data.info,
        type: 2
      });
    }

    query.synHistoryId = getParalegalDataId();

    const payFn = () => {
      toConfirmOrder(query);
    };

    payFn();

    // 将付费方法存入vuex
    Store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFn);
  } catch (e) {
    console.log(e);
  }
}

/**
 * 如果有留资信息时下公共订单，则优先使用留资信息里面的信息
 * 如果没有，则直接走服务器下单
 */
export async function payServiceCommon({
  serviceCode,
  params = {
    info: "用户未输入问题描述",
    typeLabel: "综合咨询",
    typeValue: 26,
    type: 3
  },
  paySuccessCallback,
  productPriceCoefficientKey
}) {
  try {
    const res = await caseSourceV2GetCaseOrZx();

    const data = res.data || {};

    let query = {
      serviceCode,
      paySuccessCallback,
      productPriceCoefficientKey
    };

    /** 0 无 1 案源 2 问答 */
    const type = Number(data.type);

    if (type === 0) {
      await saveParalegalData(params);

      query.synHistoryId = getParalegalDataId();
    }

    if (type === 1) {
      query.synCaseSourceId = data.id;
    }

    if (type === 2) {
      query.synWdId = data.id;
    }

    const payFn = () => {
      toConfirmOrder(query);
    };

    payFn();

    // 将付费方法存入vuex
    Store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFn);
  } catch (e) {
    console.log(e);
  }
}
