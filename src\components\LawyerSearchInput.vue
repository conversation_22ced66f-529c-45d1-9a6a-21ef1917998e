<template>
  <div class="px-[12px] flex items-center">
    <div
      :style="[
        {
          border: themeColor ? `2rpx solid ${themeColor}` : '',
          backgroundColor: bgColor
        }
      ]"
      class="rounded-[50px] h-[36px] flex-1 flex items-center"
    >
      <div class="relative">
        <div
          class="w-[86px] flex items-center justify-center "
          @click="tabState = !tabState"
        >
          <p class="text-[14px] text-[#333333]">
            {{ currentTab.searchLabel || "" }}
          </p>
          <img
            alt=""
            class="w-[16px] h-[16px] ml-[4px]"
            src="../pages/ask-details/search/img/arrow-down_black.png"
          >
        </div>
        <div
          v-show="tabState"
          class="absolute z-10 left-[0px] top-[36px] box-border px-[16px] w-[130px] bg-[#FFFFFF] rounded-[8px] [box-shadow:0px_2px_12px_0px_rgba(0,0,0,0.12)]"
        >
          <p
            v-for="item in tabs"
            :key="item.value"
            :class="[
              { 'text-[#3887F5]': searchData.searchType === item.value }
            ]"
            class="text-[14px] text-[#333333] leading-[44px] border-0 border-b-[1px] border-solid border-[#EEEEEE] last-of-type:border-b-[0]"
            @click.stop="selectSearchType(item)"
          >
            {{ item.label }}
          </p>
        </div>
      </div>
      <i class="w-[1px] h-[20px] bg-[#DDDDDD] mr-[8px]" />
      <u--input
        v-model="searchText"
        :adjustPosition="false"
        :customStyle="{ height: pxToRpx(36) }"
        class="flex-1"
        border="none"
        clearable
        placeholder="请输入内容"
        shape="circle"
        @focus="$emit('focus')"
        @confirm="handleSearch"
      />
      <p
        :style="[
          {
            color: themeColor
          }
        ]"
        class="text-[14px] pl-[12px] text-[#333333] flex-shrink-0 pr-[16px]"
        @click="handleSearch"
      >
        搜索
      </p>
    </div>
  </div>
</template>
<script>
import { pxToRpx } from "@/libs/tools";

export default {
  name: "LawyerSearchInput",
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    /** 主题色 */
    themeColor: {
      type: String,
      default: ""
    },
    /** 背景色 */
    bgColor: {
      type: String,
      default: "#F5F5F7"
    }
  },
  data() {
    return {
      /* 搜索tab*/
      tabs: [
        {
          label: "按律所名称查询",
          searchLabel: "按律所",
          value: "lawyerFirmName"
        },
        {
          label: "按律师姓名查询",
          searchLabel: "按律师",
          value: "lawyerName"
        }
      ],
      tabState: false,
      /* 搜索输入框*/
      searchText: ""
    };
  },
  computed: {
    /* 当前tab类型*/
    currentTab() {
      return (
        this.tabs.find(item => item.value === this.searchData.searchType) || {}
      );
    },
    searchData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    pxToRpx,
    handleSearch() {
      this.searchData = {
        ...this.searchData,
        searchText: this.searchText
      };

      this.$emit("search", this.searchData);
    },
    selectSearchType(data) {
      this.searchData = {
        ...this.searchData,
        searchType: data.value
      };

      this.tabState = false;

      this.$emit("selected");
    }
  }
};
</script>
