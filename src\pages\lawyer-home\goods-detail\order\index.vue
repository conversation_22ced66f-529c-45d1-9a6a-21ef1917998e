<template>
  <div>
    <goods-detail-order-swiper />
    <div class="px-[16px] space-y-[12px] mt-[12px] min-h-screen">
      <div
        class="bg-[#FFFFFF] rounded-[8px] flex items-center px-[12px] py-[12px]"
      >
        <img
          :src="productInfo.icon"
          alt=""
          class="w-[44px] h-[44px] block mr-[12px] shrink-0"
        >
        <div class="w-full">
          <div class="flex items-center justify-between">
            <div class="font-bold text-[16px] text-[#333333]">
              {{ productInfo.serviceName }}
            </div>
            <div class="text-[16px] text-[#333333]">
              ¥{{ productInfo.servicePrice | amountFilter }}
            </div>
          </div>
          <div class="text-[12px] text-[#999999] mt-[8px]">
            规格: {{ productInfo.serviceNum }}{{ productInfo.unitLabel }}
          </div>
        </div>
      </div>
      <div class="bg-[#FFFFFF] rounded-[8px] px-[16px]">
        <div
          v-for="(item, index) in infoList"
          :key="index"
          class="flex items-center justify-between py-[13px] border-0 last:border-b-0 border-b-[0.5px] border-solid border-[#EEEEEE]"
        >
          <div class="text-[14px] text-[#666666]">
            {{ item.label }}
          </div>
          <div class="text-[14px] text-[#333333]">
            {{ item.value }}
          </div>
        </div>
      </div>
      <div class="bg-[#FFFFFF] rounded-[8px] py-[12px] px-[16px]">
        <div class="font-bold text-[15px] text-[#333333] mb-[12px]">
          问题描述
        </div>
        <textarea
          :show-count="false"
          :value="params.info"
          style="font-size: 28rpx"
          class="w-full h-[140px]"
          maxlength="300"
          placeholder="请在这里留言您遇上的问题和诉求，方便为您推荐合适的律师类型"
          placeholder-style="color: #CCC;font-size: 28rpx;line-height: 32rpx;"
          @input="wordsCheck"
        />
        <!-- 底部的咨询类型按钮 -->
        <div class="mt-[16px] flex items-center justify-between">
          <div class="text-[12px] text-[#999999]">
            {{ params.info.length }}/300
          </div>
          <div
            class="p-[8px] bg-[#EBF3FE] rounded-[8px] border-[0.5px] border-solid border-[#A0CFFB] flex items-center justify-center box-border text-[13px] text-[#3887F5]"
            @click="showPopup = true"
          >
            <img
              v-if="!typeLabel"
              alt=""
              class="w-[12px] h-[12px] mr-[2px]"
              src="@/pages/lawyer-home/img/11113.png"
            >
            <span>咨询类型<span v-if="typeLabel">：{{ typeLabel }}</span></span>
          </div>
        </div>
        <app-popup-select
          :dataSource="lawyerSpeciality"
          :value="params"
          :visible.sync="showPopup"
          @confirm="handleSelect"
        />
      </div>
    </div>
    <div class="bg-[#FFFFFF] fixed left-0 bottom-0">
      <div
        class="flex items-center justify-between w-[375px] px-[16px] py-[8px] box-border"
      >
        <div>
          <div class="flex items-center">
            <div class="text-[14px] text-[#333333]">
              合计
            </div>
            <div class="font-bold text-[18px] text-[#F34747] ml-[6px]">
              ¥{{ productInfo.servicePrice | amountFilter }}
            </div>
          </div>
          <div class="text-[11px] text-[#666666] mt-[2px]">
            已优惠 ¥{{ discountPrice }}
          </div>
        </div>
        <div
          class="w-[218px] h-[44px] bg-[linear-gradient(_90deg,_#FA700D_0%,_#F34747_100%)] rounded-[22px] font-bold text-[16px] text-[#FFFFFF] flex items-center justify-center box-border"
          @click="buyNow"
        >
          提交订单
        </div>
      </div>
      <u-safe-bottom />
    </div>
    <repid-return-popup
      v-model="returnPopup"
      tips="平台将立即匹配律师，10s内接入服务，提供定制解决方案"
    />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import AppPopupSelect from "@/components/app-components/app-form-list/app-popup-select/index.vue";
import { dataDictionary } from "@/api";
import { getParalegalDataId, saveParalegalData } from "@/libs/paralegalData";
import { toConfirmOrder } from "@/libs/turnPages";
import { wordsCheckDoCheck } from "@/api/im";
import { serviceManegeInfo } from "@/api/order";
import { amountFilter } from "@/libs/filter";
import { mapGetters } from "vuex";
import GoodsDetailOrderSwiper from "@/pages/lawyer-home/goods-detail/order/GoodsDetailOrderSwiper.vue";
import RepidReturnPopup from "@/components/RepidReturnPopup.vue";
import payFailMixins from "@/mixins/payFailMixins";
import Store from "@/store";

import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "GoodsDetailOrder",
  components: {
    RepidReturnPopup,
    GoodsDetailOrderSwiper,
    AppPopupSelect,
    USafeBottom
  },
  mixins: [payFailMixins],
  data() {
    return {
      productInfo: {},
      /** 是否显示咨询类型弹窗 */
      showPopup: false,
      /** 案件类型 */
      lawyerSpeciality: [],
      params: {
        typeLabel: "",
        typeValue: null,
        info: "",
        type: 2
      },
      returnPopup: false
    };
  },

  onLoad(query) {
    const { serviceCode } = query;

    buryPointChannelBasics({
      code: "LAW_APPLET_BOTTOM_MALL_GOODS_DETAIL_PAGE_BUY_NOW_CLICK",
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1,
      extra: {
        serviceCode
      }
    });

    serviceManegeInfo(serviceCode).then(res => {
      try {
        this.productInfo = {
          ...res.data,
          serviceDesc: JSON.parse(res.data.serviceDesc)
        };
      } catch (e) {
        console.log(e);

        this.productInfo = res.data;
      }
    });
  },
  computed: {
    ...mapGetters({ getUserInfo: "user/getUserInfo" }),
    infoList() {
      return [
        {
          label: "服务律师",
          value: "由平台指派律师完成"
        },
        {
          label: "联系电话",
          value: this.getUserInfo.phone
        }
      ];
    },
    /** 优惠价格 */
    discountPrice() {
      return amountFilter(
        Number(this.productInfo.originalPrice) -
          Number(this.productInfo.servicePrice)
      );
    },
    typeLabel() {
      return this.params?.typeLabel;
    }
  },
  mounted() {
    this.getData();
  },
  methods: {
    payFailCallback() {
      this.returnPopup = true;
      console.log("payFailCallback支付失败");
    },
    /** 获取案件类型数据 */
    getData() {
      /** 案件类型 */
      dataDictionary({
        groupCode: "LAWYER_SPECIALITY"
      }).then(lawyerSpeciality => {
        lawyerSpeciality.data.forEach(item => {
          item.value = Number(item.value);

          // 默认综合咨询
          if (item.value === 26) {
            this.params.typeValue = item.value;
            this.params.typeLabel = item.label;
          }
        });

        this.lawyerSpeciality = lawyerSpeciality.data;
      });
    },
    /** 验证参数 */
    checkParams() {
      if (!this.params.typeValue) {
        uni.showToast({
          title: "请选择咨询类型",
          icon: "none"
        });

        return false;
      }

      // 判断是否是 10-300 字
      if (this.params.info.length < 10 || this.params.info.length > 300) {
        uni.showToast({
          title: "请填写10-300字的问题描述",
          icon: "none"
        });

        return false;
      }

      return true;
    },
    /** 点击提交订单 */
    async buyNow() {
      try {
        this.params.info = this.params.info.trim();

        if (!this.checkParams()) {
          return;
        }

        await wordsCheckDoCheck({
          text: this.params.info
        });

        await saveParalegalData(this.params);

        const payFn = () => {
          toConfirmOrder({
            serviceCode: this.productInfo.serviceCode,
            synHistoryId: getParalegalDataId()
          });
        };

        Store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFn);

        payFn();
      } catch (e) {
        console.log(e);
      }
    },
    handleSelect(item) {
      this.params = {
        ...this.params,
        ...item
      };
    },
    wordsCheck(e) {
      console.log(e);
      // 限定300个字符
      if (e.target.value.length > 300) {
        this.params.info = e.target.value.slice(0, 300);
        return;
      }

      this.params.info = e.target.value;
    }
  }
};
</script>
