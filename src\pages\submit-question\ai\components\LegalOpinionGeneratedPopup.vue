<template>
  <div>
    <app-popup
      :show="show"
      :closeOnClickOverlay="false"
      showCancelButton
      zIndex="9990"
      @cancel="close"
      @close="close"
    >
      <div class="p-[16px]">
        <div class="text-[20px] text-[#333333] text-center">
          专属法律意见书已生成
        </div>
        <div
          class="rounded-[8px] border-[1px] border-solid border-[#F2F3F5] mt-[12px] box-border p-[12px] box-border"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <img
                class="block w-[24px] h-[25.85px]"
                alt=""
                src="@/pages/submit-question/ai/img/book.png"
              >
              <div class="font-bold text-[16px] text-[#333333] ml-[4px]">
                法律意见书
              </div>
            </div>
            <div class="flex items-center">
              <div class="font-bold text-[16px] text-[#EB4738]">
                <span class="text-[12px]">¥</span>39.99
              </div>
              <div
                class="text-[12px] text-[#999999] [text-decoration-line:line-through] ml-[4px]"
              >
                ¥99
              </div>
            </div>
          </div>
          <div class="flex items-center justify-between mt-[8px]">
            <div>
              <div class="flex items-center">
                <img
                  class="block w-[12px] h-[12px]"
                  alt=""
                  src="@/pages/submit-question/ai/img/yv8xqt.png"
                >
                <div class="text-[13px] text-[#999999] ml-[4px]">
                  根据用户留言实时定制
                </div>
              </div>
              <div class="flex items-center mt-[8px]">
                <img
                  class="block w-[12px] h-[12px]"
                  alt=""
                  src="@/pages/submit-question/ai/img/yv8xqt.png"
                >
                <div class="text-[13px] text-[#999999] ml-[4px]">
                  提供专业法律意见指导
                </div>
              </div>
            </div>
            <div
              class="w-[98px] h-[32px] rounded-[100px] border-[1px] border-solid border-[#3887F5] flex items-center justify-center"
            >
              <div class="text-[14px] text-[#3887F5]">
                立即获取
              </div>
            </div>
          </div>
        </div>
        <div
          class="p-[12px] mt-[12px] box-border bg-[#FFF5E4] [box-shadow:0px_0px_14px_0px_rgba(255,241,219,0.8)] rounded-[8px] border-[1px] border-solid [border-image:linear-gradient(171deg,_rgba(255,_241,_219,_1),_rgba(255,_241,_219,_1))_1_1]"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <img
                class="block w-[24px] h-[24px]"
                alt=""
                src="@/pages/submit-question/ai/img/Frame1321314984.png"
              >
              <div class="font-bold text-[16px] text-[#333333] ml-[4px]">
                法律意见书+真人律师答疑
              </div>
            </div>
            div
            <div class="flex items-center">
              <div class="font-bold text-[16px] text-[#EB4738]">
                <span class="text-[12px]">¥</span>49.99
              </div>
              <div
                class="text-[12px] text-[#999999] [text-decoration-line:line-through] ml-[4px]"
              >
                ¥299
              </div>
            </div>
          </div>
          <img
            class="block w-[319px] h-[53px] mt-[12px]"
            alt=""
            src="@/pages/submit-question/ai/img/btn.png"
          >
        </div>
      </div>
      <u-safe-bottom />
    </app-popup>
    <legal-opinion-popup />
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import AiPayMixin from "@/pages/submit-question/ai/AiPayMixin.js";
import { PAGE_MARK_ENUM, POSITION_ENUM } from "@/libs/getPageInfo";
import LegalOpinionPopup from "@/pages/submit-question/ai/components/LegalOpinionPopup.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "LegalOpinionGeneratedPopup",
  components: {
    AppPopup,
    LegalOpinionPopup,
    USafeBottom,
  },
  mixins: [AiPayMixin],
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      show: false,
      pageMark: PAGE_MARK_ENUM.SUBMIT_QUESTION_AI,
      position: POSITION_ENUM.LEGAL_OPINION_GENERATED_POPUP,
      buryPointPath: 5061,
      buryPointCode: "LAW_APPLET_AI_LEGAL_OPINION_GENERATED_POPUP_PAY_CLICK",
      infoList: [
        { icon: require("@/pages/submit-question/ai/img/5kihz5.png"), label: "定制法律意见书" },
        { icon: require("@/pages/submit-question/ai/img/wmhjrb.png"), label: "真人律师1对1服务" },
        { icon: require("@/pages/submit-question/ai/img/hzd0ki.png"), label: "无限次在线沟通" },
        { icon: require("@/pages/submit-question/ai/img/2sih9f.png"), label: "平台保障 不满意随时退" },
      ],
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true,
    },
  },
  methods: {
    close() {
      this.show = false;
      this.$emit("close");
    },
    handleBasicPay() {
      // 基础版支付 - 可以传递不同的服务代码或参数
      this.buryPointCode = "LAW_APPLET_AI_LEGAL_OPINION_BASIC_PAY_CLICK";
      this.buryPointPath = 5062;
      this.pay();
    },
    handlePremiumPay() {
      // 升级版支付
      this.buryPointCode = "LAW_APPLET_AI_LEGAL_OPINION_PREMIUM_PAY_CLICK";
      this.buryPointPath = 5063;
      this.pay();
    },
  },
};
</script>
