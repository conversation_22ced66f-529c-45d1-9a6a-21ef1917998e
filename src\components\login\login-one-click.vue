<template>
  <div>
    <old-login-popup
      v-if="![0, 3].includes(lawAppletPageLoginStyleValue)"
      :agreement="check"
      :index="lawAppletPageLoginStyleValue"
      :show="show"
      @btnHandleClick="confirmLogin"
      @getPhoneNumberHandler="getPhoneNumberHandler"
      @loginPopupClose="loginPopupClose"
      @open="open"
      @protocolClick="protocolClick"
      @setAgreement="(val) => (check = val)"
    />
    <login-popup-one
      v-if="[0].includes(lawAppletPageLoginStyleValue)"
      :agreement="check"
      :index="lawAppletPageLoginStyleValue"
      :show="show"
      @btnHandleClick="confirmLogin"
      @getPhoneNumberHandler="getPhoneNumberHandler"
      @loginPopupClose="loginPopupClose"
      @open="open"
      @protocolClick="protocolClick"
      @setAgreement="(val) => (check = val)"
    />
    <login-popup-tow
      v-if="[3].includes(lawAppletPageLoginStyleValue)"
      :agreement="check"
      :index="lawAppletPageLoginStyleValue"
      :show="show"
      @btnHandleClick="confirmLogin"
      @getPhoneNumberHandler="getPhoneNumberHandler"
      @loginPopupClose="loginPopupClose"
      @open="open"
      @protocolClick="protocolClick"
      @setAgreement="(val) => (check = val)"
    />
    <app-popup
      :safeAreaInsetBottom="false"
      :show="protocolDialog"
      :zIndex="99998"
      mode="center"
    >
      <view class="protocol-dialog">
        <view class="flex-1 top">
          <view class="protocol-dialog-title">
            温馨提示
          </view>
          <view class="protocol-dialog-info">
            为更好地保障您的合法权益，请您阅读并同意
            <text
              class="color-text"
              @click="protocolClick(1)"
            >
              《隐私政策》
            </text>
            <text
              class="color-text"
              @click="protocolClick(2)"
            >
              《用户服务协议》
            </text>
          </view>
        </view>
        <view class="protocol-dialog-button">
          <view
            class="protocol-dialog-btn"
            @click="protocolDialog = false"
          >
            拒绝
          </view>
          <button
            class="protocol-dialog-btn"
            open-type="getPhoneNumber"
            @click="btnHandleClick(true)"
            @getphonenumber="getPhoneNumberHandler"
          >
            同意
          </button>
        </view>
      </view>
    </app-popup>
    <app-popup
      :safeAreaInsetBottom="false"
      :show="protocolModal"
      :zIndex="99999"
      mode="center"
    >
      <view class="protocol-modal">
        <div class="protocol-content">
          <div class="protocol-title">
            {{ protocolTitle }}
          </div>
          <scroll-view
            class="protocol-nr"
            scrollY="true"
          >
            <div style="white-space: pre-wrap; line-height: 20px">
              {{ protocolContent }}
            </div>
          </scroll-view>
        </div>
        <div
          class="protocol-btn"
          @click="protocolModal = false"
        >
          我知道了
        </div>
      </view>
    </app-popup>
    <app-popup
      :safeAreaInsetBottom="false"
      :show="loginSecondPopup"
      :zIndex="999999"
    >
      <div
        class="w-[375px] pb-[40px] pt-[28px] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none relative z-10"
      >
        <if t="hjls">
          <img
            alt=""
            class="w-[375px] h-[102px] block absolute top-0 left-0 -z-10"
            src="@/components/login/imgs/<EMAIL>"
          >
        </if>
        <div class="font-[600] text-[18px] text-[#333333] text-center">
          平台咨询需完成登录
        </div>
        <div class="text-[14px] text-[#333333] mt-[8px] text-center">
          距律师回复仅
          <span class="text-[#F34747] inline-block ml-[4px]">一步之差</span>
        </div>
        <!-- #ifdef MP-WEIXIN -->
        <button
          class="w-[327px] h-[44px] mt-[34px] bg-[#07C160] rounded-[8px] text-[16px] text-[#FFFFFF] flex items-center justify-center mx-auto box-border"
          open-type="getPhoneNumber"
          @click="btnHandleClick"
          @getphonenumber="getPhoneNumberHandler"
        >
          登录
        </button>
        <!-- #endif -->
        <!-- #ifndef MP-WEIXIN -->
        <button
          class="w-[327px] h-[44px] mt-[34px] bg-[#3887F5] rounded-[8px] text-[16px] text-[#FFFFFF] flex items-center justify-center mx-auto box-border"
          open-type="getPhoneNumber"
          @click="btnHandleClick"
          @getphonenumber="getPhoneNumberHandler"
        >
          登录
        </button>
        <!-- #endif -->
        <div
          class="text-[12px] text-[#999999] mt-[16px] text-center"
          @click="loginSecondPopupClose"
        >
          取消登录
        </div>
      </div>
    </app-popup>
    <if f="hjls">
      <server-dialog v-if="serverShow" />
    </if>
    <login-award-popup />
    <device-login />
  </div>
</template>

<script>
import LoginMixin from "@/mixins/login.js";
import { checkAppLoginAndGetCode } from "@/libs/token";
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { protocolA, protocolB } from "./js/protocol";

import { buryPointChannelBasics, loginPoint } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
import ServerDialog from "@/components/login/components/server-dialog.vue";
import discountPrice from "@/libs/discountPrice";
import { lawyerListV3 } from "@/api/findlawyer";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { ttConfirmFulfillment } from "@/libs/tools";
import LoginAwardPopup from "@/components/login/components/LoginAwardPopup.vue";
import CurrentPageMixin from "@/mixins/currentPageMixin";
import { currencyGetAddress } from "@/api";
import DeviceLogin from "@/components/login/DeviceLogin.vue";
import OldLoginPopup from "@/components/login/login-popup/OldLoginPopup.vue";
import LoginPopupOne from "@/components/login/login-popup/LoginPopupOne.vue";
import LoginPopupTow from "@/components/login/login-popup/LoginPopupTow.vue";
import { getCurrentPageRoute } from "@/libs/turnPages";
import { isPlatformIn, PLATFORM_TYPES } from "@/libs/appConfig";

export default {
  components: {
    LoginPopupTow,
    LoginPopupOne,
    OldLoginPopup,
    DeviceLogin,
    LoginAwardPopup,
    ServerDialog,
    AppPopup,
  },
  mixins: [LoginMixin, CurrentPageMixin],
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    needTransformationPath: {
      type: [Boolean, String],
      default: false,
    },
    /** 点击遮罩是否关闭弹窗 */
    closeOnClickOverlay: {
      type: Boolean,
      default: true,
    },
    /** 是否显示安全区域 */
    showSafe: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      check: false,
      protocolDialog: false,
      protocolModal: false,
      protocolTitle: "",
      protocolContent: "",
      //  {popupType: 5, type: 3, ext: {caseSourceV2Id: 21821}}
      /** 登录二级弹窗显影 */
      loginSecondPopup: false,
      /** 律师信息 */
      lawyerList: [],
      lawAppletPageLoginStyleValue: -1,
      localInfo: {},
      /** 控制 serverDialog 弹窗是否进入到了特定的页面 */
      isShowServerDialog: false,
    };
  },
  computed: {
    isLogin() {
      return !!this.$store.state.user.token;
    },
    serverShow() {


      return this.$store.state.user.loginPopupShow && this.isShowServerDialog;
    },
  },
  watch: {
    show(val) {
      if (!val) {
        this.check = false;
        this.protocolDialog = false;
        this.protocolModal = false;
      } else {
        if (this.isNotCurrentPage()) return;

        this.getLawyerInfo();

        loginPoint.buryPointLoginIn().then((data) => {
          this.lawAppletPageLoginStyleValue =
            data.lawAppletPageLoginStyleValue || 0;

          buryPointChannelBasics({
            code: "LAW_APPLET_ASK_LAWYER_LOGIN_POP_UP_PAGE",
            behavior: BURY_POINT_CHANNEL_TYPE.VI,
            type: 1,
          });

          buryPointTransformationPath.add(1071);

          buryPointChannelBasics({
            code: "LAW_APPLET_COMMON_LOGIN_POP_UP_PAGE",
            behavior: BURY_POINT_CHANNEL_TYPE.VI,
            type: 1,
          });
        });

        currencyGetAddress().then((res) => {
          this.localInfo = res.data;
        });
      }
    },
    isLogin: {
      handler(val) {
        if (!val) {
          this.serverShow = false;
        }
      },
    },
  },
  mounted() {
    this.getShowServerDialog();
  },
  methods: {
    /** 判断当前页面是否是特定的页面 */
    getShowServerDialog() {
      const { fullPath } = getCurrentPageRoute();

      const isShow = ["/pages/submit-question/findlawyer/index", "/pages/mine/index"].includes(
        fullPath
      );

      this.isShowServerDialog = isShow;
    },
    /** 获取律师信息 */
    getLawyerInfo() {
      lawyerListV3({
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 6,
      }).then((res) => {
        this.lawyerList = res.data.records || [];
      });
    },
    // 登录成功的回调，LoginMixin
    loginSuccess() {
      ttConfirmFulfillment();
      this.close();
      this.$store.commit("user/EXECUTE_LOGIN_CALLBACK");
      discountPrice.init();
      this.$store.commit("user/SET_LOGIN_POPUP_SHOW", true);
    },
    /** 点击一键登录按钮，这里是为了能够正确进行埋点 */
    confirmLogin(confirm) {
      this.btnHandleClick(confirm.flag, confirm.firstPopup);
    },
    btnHandleClick(flag, firstPopup) {
      this.check = true;
      // #ifdef MP-TOUTIAO || MP-WEIXIN
      this.protocolDialog = false;
      // #endif

      // 方法在 LoginMixin 中
      this.handleClick();

      if (firstPopup) {
        buryPointChannelBasics({
          code: "LAW_APPLET_COMMON_LOGIN_POP_UP_PAGE_LOGIN_CLICK",
          behavior: BURY_POINT_CHANNEL_TYPE.CK,
          type: 1,
        });
      } else {
        buryPointChannelBasics({
          code: "LAW_APPLET_COMMON_LOGIN_POP_UP_INTERCEPT_PAGE_CONTINUE_CLICK",
          behavior: BURY_POINT_CHANNEL_TYPE.CK,
          type: 1,
        });
      }

      // 点击协议弹框 同意按钮
      if (flag) {
        buryPointChannelBasics({
          code: POINT_CODE.LAW_APPLET_HOT_REMIND_WINDOW_ALLOW_CLICK,
          behavior: BURY_POINT_CHANNEL_TYPE.CK,
          type: 1,
        });
      }
    },
    /** 二级弹窗点击取消登录按钮 */
    loginSecondPopupClose() {
      buryPointChannelBasics({
        code: "LAW_APPLET_COMMON_LOGIN_POP_UP_INTERCEPT_PAGE_GIVE_UP_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1,
      });
      this.close();
    },
    close() {
      this.loginSecondPopup = false;
      this.protocolDialog = false;
      this.protocolModal = false;
      this.check = false;
      this.$store.commit("popup-state/SET_LOGIN_POPUP_STATE", false);
    },
    /** 点击取消登录按钮 */
    loginPopupClose() {
      buryPointTransformationPath.add(1072);

      buryPointChannelBasics({
        code: "LAW_APPLET_COMMON_LOGIN_POP_UP_PAGE_CANCEL_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1,
      });

      this.loginSecondPopup = true;

      buryPointChannelBasics({
        code: "LAW_APPLET_COMMON_LOGIN_POP_UP_INTERCEPT_PAGE",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1,
      });

      this.$store.commit("popup-state/SET_LOGIN_POPUP_STATE", false);
    },
    open() {
      // 获取授权 code
      checkAppLoginAndGetCode();
    },
    protocolClick(type) {
      if(isPlatformIn(PLATFORM_TYPES.SHALLOW_MAJIA)) {
        this.protocolTitle = type === 1 ? "法临问律师隐私政策" : "法临问律师用户服务协议";
      } else {
        this.protocolTitle = type === 1 ? "法临隐私政策" : "法临网用户服务协议";
      }
      // #ifdef MP-HJLS
      this.protocolTitle =
        type === 1
          ? "好佳律师法律咨询隐私政策"
          : "好佳律师法律咨询用户服务协议";
      // #endif
      this.protocolModal = true;
      this.protocolContent = type === 1 ? protocolA : protocolB;
    },
    // 弹出协议提示
    protocolDialogClick() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_HOT_REMIND_WINDOW,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1,
      });
      this.protocolDialog = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.color-text {
  color: #f78c3e;
}

.protocol-dialog {
  width: 311px;
  height: 174px;
  background: #ffffff;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .top {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  &-info {
    text-align: center;
    font-size: 14px;
    color: #333;
    margin-top: 12px;
    padding: 0 24px;
  }

  &-button {
    height: 60px;
    display: flex;
    justify-content: center;
    font-size: 14px;
    padding-top: 8px;

    .protocol-dialog-btn {
      text-align: center;
      width: 126px;
      height: 36px;
      line-height: 36px;
      border-radius: 68px 68px 68px 68px;

      &:nth-child(1) {
        color: #333;
        margin-right: 12px;
        border: 1px solid #cccccc;
      }

      &:nth-child(2) {
        color: #fff;
        background: $theme-color;
        font-size: 14px;
      }
    }
  }
}

.protocol-modal {
  width: 311px;
  background: #fff;
  border-radius: 16px;

  .protocol-content {
    padding: 24px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 16px;

    .protocol-title {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 12px;
      text-align: center;
    }

    .protocol-nr {
      height: 300px;
    }
  }

  .protocol-btn {
    height: 46px;
    line-height: 46px;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    color: #3887f5;
    border-top: 1px solid #eeeeee;
  }
}
</style>
