import { createOrder, orderPay, serviceManegeInfo, serviceManegeInfoNew } from "@/api/order";
import Store from "@/store";

import { servicePayStatus } from "@/api";
import { TRANSFORMATION_PATH } from "@/libs/config";
import {
  bindWechatAppId,
  debounce,
  filterObjUndefined,
  ttConfirmFulfillment,
} from "@/libs/tools";
import { getCurrentPageRoute } from "@/libs/turnPages";

function filterUndefined(val) {
  if (val && val !== "undefined") {
    return val;
  } else {
    return null;
  }
}

// 订单业务类型
function getTypeParam(serviceData) {
  const { type = 1 } = serviceData;
  let typeParam = 1;
  switch (type) {
  case 1:
  case 3:
    typeParam = 2;
    break;
  case 2:
    typeParam = 1;
    break;
  case 4:
    typeParam = 3;
    break;
  case 5:
    typeParam = 4;
    break;
  case 6: // 免费追问过来
    typeParam = 5;
    break;
    // 悬赏问答
  case 7:
    typeParam = 6;
    break;
    /** 服务悬赏 */
  case 8:
    typeParam = 7;
    break;
    /* 合同模板*/
  case 9:
    typeParam = 8;
    break;
  // 法律意见书
  case 10:
    typeParam = 9;
    break;
  // ai畅聊
  case 11:
    typeParam = 10;
    break;
  // 打官司付定金
  case 12:
    typeParam = 11;
    break;
  // 打官司付尾款
  case 13:
    typeParam = 12;
    break;
  }

  return typeParam;
}

async function toPayNow(pageOptions) {
  await bindWechatAppId();

  let { orderId, paySuccessCallback } = pageOptions;

  const storageOrder = uni.getStorageSync("orderId");

  let storageOrderIdHistory;

  const optionsFilter = filterObjUndefined(pageOptions);

  console.log(optionsFilter, "optionsFilter");
  console.log(storageOrder, "storageOrder");

  // * 遍历 pageOptions 中的值，与 storageOrder 进行比较，如果有值不同，则将 storageOrderIdHistory 赋值为 null 并且跳出循环
  for (const key in optionsFilter) {
    if (optionsFilter.hasOwnProperty(key)) {
      if (storageOrder && storageOrder[key] !== optionsFilter[key]) {
        storageOrderIdHistory = null;
        break;
      } else {
        // 如果没有跳出循环，则将 storageOrder 中的 orderId 赋值给 storageOrderIdHistory
        storageOrderIdHistory = storageOrder?.orderId;
      }
    }
  }

  orderId = filterUndefined(orderId) || storageOrderIdHistory;

  if (orderId) {
    toPayMethods(orderId, { paySuccessCallback });
  } else {
    toCreateOrder(pageOptions);
  }
}

function toPayMethods(orderId, { paySuccessCallback } = {}) {
  orderPay({
    // 头条
    // #ifdef MP-TOUTIAO
    payType: 17,
    // #endif

    // 微信
    // #ifdef MP-WEIXIN
    payType: 16,
    openid: Store.getters["user/getOpenid"],
    // #endif

    // 支付宝
    // #ifdef MP-ALIPAY
    payType: 15,
    openid: Store.getters["user/getOpenid"],
    // #endif

    // 百度
    // #ifdef MP-BAIDU
    payType: 14,
    // #endif
    orderId,
  })
    .then(async ({ data }) => {
      uni.hideLoading();

      const jump = () => {
        if (!paySuccessCallback) {
          Store.commit("payState/PAY_SUCCESS", { orderId });
          uni.removeStorageSync("orderId");
          uni.redirectTo({
            url: "/pages/pay/pay-success?id=" + orderId,
          });
        } else {
          Store.commit("payState/PAY_SUCCESS", { orderId });
          ttConfirmFulfillment()
            .then(() => {
              uni.removeStorageSync("orderId");
              paySuccessCallback && paySuccessCallback();
            })
            .catch(() => {
              // 如果用户拒绝使用，则刷新当前页面，重置页面的状态
              uni.reLaunch({
                url: getCurrentPageRoute().fullPath,
              });
            });
        }
      };

      // 如果是免费订单，则直接支付成功
      if (data.free) {
        jump();
        return;
      }

      let res = {};

      try {
        res = JSON.parse(data.payResultMsg);

        console.log(res, "res转换结果");
      } catch (e) {
        console.log(e);
      }
      const [, { provider }] = await uni.getProvider({
        service: "oauth",
      });

      // #ifdef MP-TOUTIAO
      console.log("抖音通用支付");
      tt.requestOrder({
        data: data.payResultMsg,
        byteAuthorization: data.byteAuthorization,
        success: (res) => {
          tt.getOrderPayment({
            // 获取orderId订单号后发起支付
            orderId: res.orderId,
            success(res) {
              console.log(res, "抖音支付调用成功回调");
              // 当前时间戳
              const now = new Date().getTime();
              // ! 当前回调不会返回支付状态，需要自己去查询
              const queryTimer = setInterval(() => {
                // 1分钟后停止查询
                if (new Date().getTime() - now > 60000) {
                  clearInterval(queryTimer);
                }

                servicePayStatus({
                  orderId,
                }).then(({ data }) => {
                  if (data.status === 1001) {
                    clearInterval(queryTimer);
                    jump();
                  }
                });
              }, 1000);
            },
            fail(err) {
              console.log(err);
            },
          });
        },
        fail: (err) => {
          console.log("fail:" + JSON.stringify(err));
        },
      });
      // #endif

      // #ifndef MP-TOUTIAO
      uni.requestPayment({
        provider: provider[0],

        // 微信
        // #ifdef MP-WEIXIN
        ...res,
        // #endif

        // 百度
        // #ifdef MP-BAIDU
        orderInfo: res,
        // #endif

        // 支付宝
        // #ifdef MP-ALIPAY
        orderInfo: res.alipay_trade_create_response.trade_no,
        // #endif
        success: function(res) {
          console.log("success:" + JSON.stringify(res));

          // ! 支付宝 小程序即便支付失败，也会返回成功
          // #ifdef MP-ALIPAY
          console.log(res, "支付宝支付回调");
          // https://opendocs.alipay.com/mini/05xhsr?pathHash=d4709298
          const aliPayResFail = [4, 4000, 6001, 6002, 6004];

          if (aliPayResFail.includes(Number(res.resultCode))) {
            uni.showToast({
              title: res.memo || "支付失败",
              duration: 2000,
              icon: "none",
            });
            Store.commit("payState/PAY_FAIL");
          } else {
            jump();
          }
          // #endif

          // 微信
          // #ifdef MP-WEIXIN
          jump();
          // #endif

          // 百度
          // #ifdef MP-BAIDU
          setTimeout(() => {
            jump();
          }, 500);
          // #endif
        },
        fail: function(err) {
          console.log(err);
          Store.commit("payState/PAY_FAIL");
          uni.showToast({
            title: "支付失败",
            duration: 2000,
            icon: "none",
          });
        },
      });
      // #endif
    })
    .catch((err) => {
      // {code: 10, message: "操作频繁，请稍后再试！", ok: false}
      // ! 订单关闭时也会返回 10 这个错误
      // if(err.code !== 10) {
      if (err.message !== "操作频繁，请稍后再试！") {
        uni.removeStorageSync("orderId");
      }
      // }
    });
}

/** 获取上一次支付的服务缓存 */
export function getStorageOrder() {
  return uni.getStorageSync("orderId");
}

/** 获取缓存中的serviceCode对应得服务信息 */
export async function getStorageServiceCode() {
  const storageOrder = uni.getStorageSync("orderId");

  if (storageOrder) {
    const { serviceCode, lawyerId, productPriceCoefficientKey } = storageOrder;

    const { data: serviceData } = await serviceManegeInfoNew(
      serviceCode,
      {
        lawyerId: filterUndefined(lawyerId),
        needProductPriceCoefficientKey: productPriceCoefficientKey ? 1 : 0
      }
    );

    return serviceData;
  }
}

async function toCreateOrder(options) {
  const {
    serviceCode,
    businessId,
    lawyerId,
    synHistoryId,
    synWdId,
    synCaseSourceId,
    authChangeLawyer,
    paySuccessCallback,
    productPriceCoefficientKey
  } = options;

  // 过滤 options 空值
  const optionsFilter = filterObjUndefined(options);

  try {
    const { data: serviceData } = await serviceManegeInfo(
      serviceCode,
      filterUndefined(lawyerId)
    );
    let respOrder = await createOrder({
      serviceCode,
      businessId: filterUndefined(businessId),
      type: getTypeParam(serviceData),
      lawyerId: filterUndefined(lawyerId),
      synHistoryId: filterUndefined(synHistoryId),
      synWdId: filterUndefined(synWdId),
      synCaseSourceId: filterUndefined(synCaseSourceId),
      productPriceCoefficientKey: filterUndefined(productPriceCoefficientKey),
      pay_position: TRANSFORMATION_PATH[Number(serviceCode)],
      authChangeLawyer,
    });
    if (respOrder) {
      // 缓存订单id
      toPayMethods(respOrder.data.orderId, { paySuccessCallback });

      uni.setStorageSync("orderId", {
        orderId: respOrder.data.orderId,
        ...optionsFilter,
      });
    }
  } catch (error) {
    console.log(error);
  }
}

const debounceToPayNow = debounce(toPayNow, 500);

export function handleOrderPay(pageOptions) {
  debounceToPayNow(pageOptions);
}
