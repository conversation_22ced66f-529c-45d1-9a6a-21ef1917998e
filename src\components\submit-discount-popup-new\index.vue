<template>
  <div>
    <app-popup
      :closeOnClickOverlay="false"
      :show="show"
      :zIndex="9997"
      showCancelButton
      @cancel="handleCancelPay"
    >
      <div class="p-[32px_16px_16px_16px]">
        <div class="flex">
          <img
            alt=""
            class="w-[18px] h-[80px] inline-block shrink-0"
            src="@/components/submit-discount-popup-new/img/<EMAIL>"
          >
          <div class="ml-[12px]">
            <div class="text-[16px] font-bold text-[#333333]">
              问题发布成功
            </div>
            <div class="text-[12px] text-[#666666] mt-[4px]">
              问题发生地：{{ resultData.regionName }}
            </div>
            <div
              class="text-[12px] text-[#999999] mt-[4px] text-ellipsis w-[315px]"
            >
              <span class="text-[#3887F5] mr-[4px]">#{{ resultData.typeLabel }}</span>{{ resultData.info }}
            </div>
          </div>
        </div>
        <div class="flex">
          <img
            alt=""
            class="w-[18px] h-[101px] inline-block shrink-0"
            src="@/components/submit-discount-popup-new/img/<EMAIL>"
          >
          <div class="ml-[12px] w-full">
            <div class="text-[14px] font-bold text-[#333333]">
              以下<span class="text-[#F78C3E]">{{ resultData.regionName }}</span>地区的<span class="text-[#F78C3E]">{{
                resultData.typeLabel
              }}</span>律师正在等待接入
            </div>
            <div class="w-full grid grid-cols-3 gap-x-[9px] mt-[14px]">
              <div
                v-for="item in allList"
                :key="item.lawyerId"
              >
                <div class="relative w-[32px] h-[32px] mx-auto">
                  <img
                    :src="item.imgUrl"
                    mode="aspectFill"
                    alt=""
                    class="w-[32px] h-[32px] rounded-[16px]"
                  >
                  <i
                    class="absolute right-0 bottom-0 rounded-full w-[8px] h-[8px] bg-[#22BF7E] border-[1px] border-solid border-[#FFFFFF]"
                  />
                </div>
                <div
                  class="text-[13px] font-bold text-[#333333] mt-[4px] text-center"
                >
                  {{ item.realName }}
                </div>
                <div
                  class="text-[10px] text-[#999999] text-ellipsis mt-[4px] text-center"
                >
                  {{ item.lawyerOffice }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-[36px]">
          <div class="flex items-center justify-between">
            <div class="text-[13px] text-[#666666]">
              选择服务：
            </div>
            <div class="text-[13px] text-[#333333]">
              {{ serviceData.serviceName }}
            </div>
          </div>
          <div class="flex items-center justify-between mt-[10px]">
            <div class="text-[13px] text-[#666666]">
              应付金额：
            </div>
            <div class="text-[15px] text-[#F34747] font-bold">
              ¥{{ serviceData.servicePrice | amountFilter }}
            </div>
          </div>
        </div>
        <div
          class="w-[343px] h-[44px] text-[16px] bg-[#3887F5_noc] mt-[17px] rounded-[40px] text-[#FFFFFF] flex items-center justify-center"
          @click="continuePay"
        >
          <span class="font-bold">继续支付</span><span class="text-[12px]">（未服务100%退款）</span>
        </div>
        <div
          class="w-[343px] h-[44px] bg-[#FFFFFF] rounded-[40px] flex items-center justify-center text-[13px] text-[#999999] mt-[12px] border border-solid border-[#EEEEEE]"
          @click="handleCancelPay"
        >
          取消支付
        </div>
      </div>
      <u-safe-bottom />
    </app-popup>
    <!-- 返回拦截弹窗 -->
    <app-popup
      :closeOnClickOverlay="false"
      :safeAreaInsetBottom="true"
      :show="discountPopup"
      :zIndex="9998"
      mode="center"
    >
      <div class="w-311px h-400px pt-149px position-relative border-box">
        <img
          alt=""
          class="background-image"
          src="@/components/submit-discount-popup-new/img/<EMAIL>"
        >
        <img
          alt=""
          class="w-24px h-24px block absolute right-11px top-65px"
          src="img/2.png"
          @click="close"
        >
        <div class="text-center text-20px font-bold text-[#333333]">
          您已获得<span class="text-[#F34747]">新人{{ discountPrice }}元优惠</span>
        </div>
        <div class="flex justify-center items-center mt-13px">
          <div class="text-12px text-[#F34747] mr-6px">
            优惠倒计时
          </div>
          <card-result-countdown
            v-if="countDown > 0"
            :time="countDown"
          />
        </div>
        <div class="flex justify-center mt-22px">
          <div class="relative flex items-center justify-center">
            <div
              class="py-[1px] px-[6px] block absolute -top-10px right-0 text-[10px] text-[#FFFFFF] bg-[linear-gradient(90deg,_#FE6128_0%,_#FF8213_100%)] rounded-tl-[8px] rounded-br-[8px] rounded-tr-[8px] rounded-bl-[1px]"
            >
              已优惠{{ discountPrice }}元
            </div>
            <div class="text-20px text-[#999999] line-through">
              ¥{{ serviceInfo.originalPrice | amountFilter }}
            </div>
            <img
              alt=""
              class="w-22px h-22px block ml-3px mr-8px"
              src="img/Frame1321315294.png"
            >
            <div class="text-32px text-[#F34747] text-bold">
              ¥{{ serviceInfo.servicePrice | amountFilter }}
            </div>
          </div>
        </div>
        <img
          alt=""
          class="w-262px block mx-auto mt-23px"
          mode="widthFix"
          src="@/components/submit-discount-popup-new/img/1.png"
          @click="toPay"
        >
        <div class="flex justify-center items-center mt-10px">
          <img
            alt=""
            class="w-42px h-16px block"
            src="@/components/submit-discount-popup-new/img/<EMAIL>"
          >
          <div class="text-12px text-[#666666] ml-4px">
            当前<span class="text-[#EB4738]">3562</span>
            位律师在线,支付后立即咨询
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import DiscountPriceMixin from "@/pages/submit-question/mixins/DiscountPriceMixin";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import CardResultCountdown from "@/components/CardResultCountdown.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { getStorageServiceCode } from "@/libs/pay";

import { lawyerListV3 } from "@/api/findlawyer";
import Store from "@/store";
import { sceneType } from "@/libs/config";
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import countDownMixin from "@/pages/rapid-consultation-confirm-order/my-consultation/js/countDownMixin";
import { amountFilter } from "@/libs/filter";
import { toSpecialSession } from "@/libs/turnPages";
import { mapGetters } from "vuex";
import { turnToBePay } from "@/pages/submit-question/to-be-paid";

export default {
  name: "SubmitDiscountPopupNew",
  components: { USafeBottom, CardResultCountdown, AppPopup },
  mixins: [DiscountPriceMixin, countDownMixin],
  props: {
    currentData: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Boolean,
      default: false
    },
    /** 展示uv code */
    uvCode: {
      type: String,
      required: true
    },
    /** 支付code */
    payCode: {
      type: String,
      required: true
    },
    /** 取消code */
    cancelCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      serviceData: {},
      /** 律师信息 */
      lawyerList: [],
      /** 控制降价策略弹窗 */
      discountPopup: false,
      serviceSceneType: sceneType.wls_jj,
      /** 留资结果 */
      resultData: {}
    };
  },
  computed: {
    ...mapGetters({
      selectLawyer: "payState/getSelectLawyer"
    }),
    allList() {
      return this.selectLawyer || this.lawyerList;
    },
    /** 弹窗显示 */
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    },
    discountPrice() {
      return amountFilter(
        this.serviceInfo.originalPrice - this.serviceInfo.servicePrice
      );
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          buryPointChannelBasics({
            code: "LAW_APPLET_PAY_RETENTION_POP_UP_PAGE",
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.CK
          });

          getStorageServiceCode().then(res => {
            this.serviceData = res;
          });

          this.getResultData().then(() => {
            // 这里尝试兼容百度小程序
            this.$nextTick(() => {
              this.getTabData();
            });
          });

          buryPointChannelBasics({
            code: this.uvCode,
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.VI
          });
        } else {
          this.$store.commit("payState/CLEAR_SELECT_LAWYER");
        }
      },
      immediate: true
    }
  },
  methods: {
    async getResultData() {
      return caseSourceV2GetCaseOrZx().then(res => {
        this.resultData = res.data || {};
      });
    },
    /** 获取数据 */
    async getTabData() {
      let params = {
        proviceCode: this.resultData.provinceCode
      };

      lawyerListV3({
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 3,
        sort: 1,
        ...params
      }).then(res => {
        this.lawyerList = res.data.records || [];
      });
    },
    close() {
      buryPointChannelBasics({
        code: this.cancelCode,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      this.discountPopup = false;

      this.$emit("close", false);
    },
    toPay() {
      buryPointChannelBasics({
        code: this.payCode,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      this.toConsult();
    },
    /** 点击继续支付 */
    continuePay() {
      Store.commit("payState/PAY_FAIL_POPUP_PAY");
    },
    /** 点击取消支付 */
    handleCancelPay() {
      this.show = false;

      buryPointChannelBasics({
        code: "LAW_APPLET_ASK_LAWYER_ANY_PAY_POPUP_CLOSE_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      turnToBePay();

      // if (!store.getters["discountStore/getIsQuestionFinish"]) return;

      // https://lanhuapp.com/web/#/item/project/product?pid=1c96f88a-eb19-4259-aa14-a21aa3d8bbbb&teamId=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=8e532db7-bd37-410c-aa66-96731b50b06f&docId=6fa14454-e7c5-4849-b432-53ca3abb208f&docType=axure&pageId=6a6da4c01b3e4898aa3aed5a6f2c22aa&image_id=6fa14454-e7c5-4849-b432-53ca3abb208f&parentId=766c109a-793d-4cdd-bc30-654a63495541
      // if (amountFilter(this.serviceData.servicePrice) >= 49.9)
      //   this.discountPopup = true;
    },
    toSpecialSession() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_ASK_LAWYER_ANY_PAY_POPUP_GO_TO_XX_CONSULT_CHECK_LAWYER_ANSWER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        extra: {
          typeLabel: this.resultData.typeLabel,
          typeValue: this.resultData.typeValue
        }
      });

      toSpecialSession({
        typeValue: this.resultData.typeValue
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
