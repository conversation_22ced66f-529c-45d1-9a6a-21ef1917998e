<template>
  <login-layout>
    <ai-lawyer-video @ended="handleVideoEnded" />
    <div
      class="overflow-hidden rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none -mt-[24px]"
    >
      <submit-layout
        ref="imLayout"
        :showOnlineStatus="false"
        :inputContainerDisable="inputContainerDisable"
        :inputContainerState="inputContainerState"
        :lawyerInfo="getLawyerInfo"
        :logoState="false"
        :meInfo="getMeInfo"
        :moreFeaturesState="false"
        :msg="msg"
        :newsList="currentData.newsList"
        hiddenHeader
        @selectCity="selectCity"
      >
        <template #bottomChatroom>
          <div
            v-if="isPaid"
            class="flex items-center justify-center"
          >
            <div class="text-[14px] text-[#4C5767]">
              你已购买AI畅聊服务，剩余次数<span class="text-[#3887F5]">{{ remainTimes }}</span>次
            </div>
          </div>
        </template>
        <template #input>
          <lawyer-fake-info
            :placeholder="placeholderText"
            :disabledPlaceholder="disabledPlaceholderText"
            :disabled="buttonDisabled"
            @focus="handleInputFocus"
            @submit="sendMsg"
          />
        </template>
      </submit-layout>
    </div>
    <app-address-base
      :isDefaultCity="false"
      :visible.sync="addressVisible"
      @input="selectCity"
    />
    <lawyer-im-pay-card-ai-ma-jia />
    <legal-opinion-generated-popup />
  </login-layout>
</template>

<script>
import { buryPointChannelBasics } from "@/api/burypoint";
import { aiEngineAskUnStreamAiChatRemainTimes } from "@/api/common";
import {
  caseSourceV2Save,
  consultationAliContent,
  servicePayStatus,
} from "@/api";
import { orderUserOrdersList } from "@/api/order";
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import { getPhoneAddress } from "@/api/user";
import AppAddressBase from "@/components/app-components/app-form-list/app-address/app-address-base.vue";
import LoginLayout from "@/components/login/login-layout.vue";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { getLocation } from "@/libs/getLocation";
import { verifyLogin } from "@/libs/login";
import { paralegalTools } from "@/libs/paralegalTools";
import { whetherToLogIn } from "@/libs/tools";
import { webImSendType } from "@/libs/web-im-tools";
import AiLawyerVideo from "@/pages/submit-question/ai/components/AiLawyerVideo.vue";
import LawyerImPayCardAiMaJia from "@/pages/submit-question/ai/components/LawyerImPayCardAiMaJia.vue";
import LegalOpinionGeneratedPopup from "@/pages/submit-question/ai/components/LegalOpinionGeneratedPopup.vue";
import {
  clearSubmitAiHistory,
  getSubmitAiHistory,
  saveSubmitAiHistory,
} from "@/pages/submit-question/ai/index";
import SubmitLayout from "@/pages/submit-question/components/submit-layout.vue";
import { scrollToBottom } from "@/pages/submit-question/js";
import LawyerFakeInfo from "@/pages/submit-question/lawyer-fake/components/LawyerFakeInfo.vue";
// https://lanhuapp.com/web/#/item/project/product?pid=9ed974f8-ce56-4ccc-8c1a-ceec8005c914&image_id=dcb1d6a1-e740-472f-89d7-8d0f8873a558&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=291e1487-8d7b-4ef7-9da2-0964a3401d8b&docId=dcb1d6a1-e740-472f-89d7-8d0f8873a558&docType=axure&pageId=6f71e17223d14b429a9233cde2f0ec21&parentId=35d8a068-34b4-478f-a06d-7b0daebf27f2
export default {
  name: "SubmitQuestionAIMaJia",
  components: {
    LoginLayout,
    SubmitLayout,
    LawyerFakeInfo,
    AppAddressBase,
    LawyerImPayCardAiMaJia,
    AiLawyerVideo,
    LegalOpinionGeneratedPopup,
  },
  data() {
    return {
      currentData: {
        newsList: [],
        aiList: [],
        caseInfo: {},
        catchList: [],
      },
      inputContainerState: true,
      inputContainerDisable: false,
      paralegalTools: null,
      remainTimes: 3,
      chatInfo: {},
      addressVisible: false,
      waitSaveCaseInfoCallback: null,
      /** Ai是否还在回复 */
      isAiReply: false,
      /** 视频是否结束 */
      isVideoEnded: false,
    };
  },
  computed: {
    buttonDisabled() {
      return this.isAiReply || this.remainTimes <= 0;
    },
    isPaid() {
      return this.chatInfo.type === "paid";
    },
    placeholderText() {
      const text = "请输入你想咨询的法律问题";

      if (this.isPaid) {
        return text;
      } else {
        return text + `（免费${this.remainTimes}次）`;
      }
    },
    disabledPlaceholderText() {
      if (this.remainTimes <= 0) {
        return "购买畅聊卡可继续咨询";
      }

      return "法临AI律师正在回复，请稍后...";
    },
    /* 当前用户信息*/
    getMeInfo() {
      return {
        avatarurl: require("@/assets/imgs/common/user-img.png"),
        imUserName: this.getUsername,
      };
    },
    getUsername() {
      // 生成一个随机id
      return "IAateLVdMvqTNWh9";
    },
    getLawyerName() {
      // 生成一个随机id
      return "3FWh7G2ZPjQoHuOq";
    },
    getLawyerInfo() {
      return {
        ext: {},
        imUserName: this.getLawyerName,
        realName: "法临AI律师",
        avatarurl: require("@/pages/submit-question/imgs/Frame1321314984.png"),
      };
    },
  },
  watch: {
    currentData: {
      handler(newVal) {
        saveSubmitAiHistory(newVal);
      },
      deep: true,
    },
    "currentData.newsList": {
      handler() {
        // 滚动到最底部
        this.$nextTick(() => {
          scrollToBottom();
        });
      },
      deep: true,
    },
  },
  async mounted() {
    buryPointChannelBasics({
      code: "LAW_APPLET_AI_ASK_PAGE_UV",
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
    });

    this.$store.commit("payState/SET_SUCCESS_CALLBACK", ({ orderId }) => {
      console.log("支付成功，触发回调", orderId);
      this.handlePayCallback(orderId);
    });

    if (!verifyLogin()) {
      await this.initListBeforeLogin();
    } else {
      await this.initList();
    }
  },
  destroyed() {
    this.$store.commit("payState/SET_SUCCESS_CALLBACK", null);
  },
  methods: {
    /**
     * 获取支付成功 对应的卡片
     * 这里主要是针对没有缓存的情况
     */
    async paySuccessCard() {
      const { data: orderList } = await orderUserOrdersList({
        queryType: 1,
        status: 1001,
      });

      const list = orderList
        .filter((item) => {
          /** 判断有没有支付过意见书 */
          const { type } = item;

          if (type === 9) {
            return true;
          }

          return false;
        })
        .map((item) => {
          return this.paralegalTools.addReceiveData({
            type: "custom",
            data: {
              style: webImSendType.SUBMIT_AI_LEGAL_OPINION_SUCCESS,
            },
            otherData: {
              orderId: item.id,
            },
          });
        });

      await this.paralegalTools.delayAddData(this.currentData.newsList, list);
    },
    handleInputFocus() {
      buryPointChannelBasics({
        code: "LAW_APPLET_AI_ASK_PAGE_INPUT_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });
    },
    readCache() {
      const history = getSubmitAiHistory() || {};
      // 这里使用深拷贝，防止catchList被污染导致出现意外的缓存
      const catchList = JSON.parse(JSON.stringify(history.catchList || []));

      if (catchList?.length > 0) {
        this.currentData = history;
        this.currentData.newsList = catchList;

        return true;
      }

      return false;
    },
    async selectCity(data) {
      this.currentData.caseInfo.cityInfo = data;
      await this.saveCaseInfo();
      await this.waitSaveCaseInfoCallback?.();
    },
    /**
     * 查询免费畅聊次数
     */
    async getFreeChatTimes() {
      try {
        const res = await aiEngineAskUnStreamAiChatRemainTimes();
        console.log("查询免费畅聊次数", res);
        return res.data;
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * 处理支付回调
     */
    async handlePayCallback(orderId) {
      try {
        const { data } = await servicePayStatus({ orderId });
        // 2是公共咨询 9是意见书 10是ai畅聊
        if (data.type === 2) {
          await this.paralegalTools.delayAddData(this.currentData.newsList, [
            this.paralegalTools.addReceiveData({
              type: "text",
              data:
                "<div>支付成功！律师将在3分钟内向您回电，请保持电话畅通，同时请注意短信提醒！<span style=\"color: #3887F5;\">点击输入框，继续向我提问</span></div>",
            }),
          ]);
        } else if (data.type === 9) {
          await this.paralegalTools.delayAddData(this.currentData.newsList, [
            this.paralegalTools.addReceiveData({
              type: "custom",
              data: {
                style: webImSendType.SUBMIT_AI_LEGAL_OPINION_SUCCESS,
              },
              otherData: {
                orderId,
              },
            }),
          ]);
        } else if (data.type === 10) {
          // 刷新次数
          const chatInfo = await this.getFreeChatTimes();
          this.refreshChatInfo(chatInfo);

          await this.paralegalTools.delayAddData(this.currentData.newsList, [
            this.paralegalTools.addReceiveData({
              type: "text",
              data: `<div>AI律师在线畅聊解锁成功！聊天次数 <span style="color: #3887F5;">+${chatInfo.remainTimes}</span> 现在继续向我提问吧~</div>`,
            }),
          ]);
        }
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * 入库逻辑
     * 如果没有入库，则第一条消息为入库，其它消息为更改案源信息
     * ! 这里不需要捕获错误，因为错误会在调用该方法的时候进行捕获
     */
    async saveCaseInfoBefore() {
      try {
        // 如果已经付费了，则不再更新案源信息
        if (this.isPaid) {
          console.log("已经付费了，不再更新案源信息");
          return;
        }

        console.log("保存案源中");
        const { data: caseSource } = await caseSourceV2GetCaseOrZx();

        // 如果已经有案源，那么只需要传入案源信息进行更新
        if (Number(caseSource.type) !== 0) {
          await caseSourceV2Save({
            info: this.currentData.caseInfo?.info,
          });

          return;
        }

        console.log("没有案源，获取定位信息");

        const { data: cityInfo = {} } = await getPhoneAddress();

        console.log(cityInfo, "cityInfo");

        // 如果有定位
        const wechatCityInfo = (await getLocation()) || {};

        console.log(wechatCityInfo, "wechatCityInfo");

        if (String(cityInfo.cityCode) === String(wechatCityInfo.cityCode)) {
          // 有定位
          this.currentData.caseInfo.cityInfo = cityInfo;

          await this.saveCaseInfo();
        } else {
          // 抛出错误
          throw new Error("请选择您所在的地区");
        }
      } catch (error) {
        // 没有定位
        this.addressVisible = true;
        throw new Error(error);
      }
    },
    async saveCaseInfo() {
      const { data } = await consultationAliContent({
        description: this.currentData.caseInfo.info,
      });

      this.currentData.caseInfo.typeLabel = data.typeLabel;
      this.currentData.caseInfo.typeValue = data.typeValue;

      await caseSourceV2Save({
        info: this.currentData.caseInfo?.info,
        typeLabel: this.currentData.caseInfo?.typeLabel,
        typeValue: this.currentData.caseInfo?.typeValue,
        amountGrade: 0,
        happenAddress:
          (this.currentData.caseInfo?.cityInfo?.provinceName || "") +
          (this.currentData.caseInfo?.cityInfo?.cityName || ""),
        regionCode: this.currentData.caseInfo?.cityInfo?.cityCode,
        squareShow: 1,
        isDistributed: false,
      });
    },
    /**
     * 发送支付卡片
     */
    async sendPayCard(data) {
      const { remainTimes } = data;

      console.log(remainTimes, "remainTimes");

      if (remainTimes === 2 && !this.isPaid) {
        buryPointChannelBasics({
          code: "LAW_APPLET_AI_ASK_PAGE_ONE_TO_ONE_CARD_CONSULT_PAGE",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI,
        });

        //  1.当用户成功剩余2条信息，AI回复完之后，触发固定引导语：如需全面的法律帮助，可电话咨询本地律师+1V1电话咨询卡片
        await this.paralegalTools.delayAddData(this.currentData.newsList, [
          this.paralegalTools.addReceiveData({
            type: "custom",
            data: {
              style: webImSendType.SUBMIT_AI_PUBLIC_CONSULTATION,
            },
          }),
        ]);
      } else if (remainTimes === 1 && !this.isPaid) {
        buryPointChannelBasics({
          code: "LAW_APPLET_AI_ASK_PAGE_LEGAL_OPINION_CARD_CLICK",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI,
        });

        // 2.当用户成功剩余1条信息，AI回复完之后，触发固定引导语：在线法律意见书，为您量身定制专属解决方案+法律意见书支付卡片
        await this.paralegalTools.delayAddData(this.currentData.newsList, [
          this.paralegalTools.addReceiveData({
            type: "custom",
            data: {
              style: webImSendType.SUBMIT_AI_LEGAL_OPINION,
            },
          }),
        ]);
      }
      // 只要剩余次数为0，都会触发支付卡片
      else if (remainTimes === 0) {
        buryPointChannelBasics({
          code: "LAW_APPLET_AI_ASK_PAGE_AI_LAWYER_CHAT_CARD_CLICK",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI,
        });
        // 3.当用户成功剩余0条信息，AI回复完之后，触发固定引导语：您的会话次数已用完，解锁AI律师深入咨询+AI律师在线畅聊支付卡片
        await this.paralegalTools.delayAddData(this.currentData.newsList, [
          this.paralegalTools.addReceiveData({
            type: "custom",
            data: {
              style: webImSendType.SUBMIT_AI_CHAT,
            },
          }),
        ]);
      }
    },
    initParalegalTools() {
      this.paralegalTools = paralegalTools(
        this.getLawyerName,
        this.getUsername,
        {
          catchList: this.currentData.catchList,
          notCatchComponentsNameList: [webImSendType.SUBMIT_AI_TEXT],
        }
      );
    },
    async initListBeforeLogin() {
      this.initParalegalTools();

      await this.paralegalTools.delayAddData(this.currentData.newsList, [
        this.paralegalTools.addReceiveData({
          type: "text",
          data: "您好，我是法临助手，可以为您提供法律意见指导，请描述您的问题吧~",
        }),
      ]);
    },
    /**
     * 初始化列表
     */
    async initList() {
      // * 初始化聊天信息的时候从接口中获取，后续的聊天信息直接从ai回复接口中获取
      const chatInfo = await this.getFreeChatTimes();
      this.refreshChatInfo(chatInfo);

      const hasCache = this.readCache();

      // 这个赋值必须要放在读取缓存之后，不然会导致引用错误
      this.initParalegalTools();

      if (hasCache) {
        return;
      }

      await this.paralegalTools.delayAddData(this.currentData.newsList, [
        this.paralegalTools.addReceiveData({
          type: "text",
          data: "您好，我是法临助手，可以为您提供法律意见指导，请描述您的问题吧~",
        }),
      ]);

      await this.sendPayCard(chatInfo);

      // 没有缓存，尝试请求是否支付过法律意见书
      await this.paySuccessCard();
    },
    /**
     * 发送消息到AI
     */
    sendMsgToAi(info) {
      this.isAiReply = true;
      this.currentData.aiList.push({
        role: "user",
        info: info,
      });

      return new Promise((resolve) => {
        this.paralegalTools.promise(
          this.currentData.newsList,
          this.paralegalTools.addReceiveData({
            type: "custom",
            data: {
              style: webImSendType.SUBMIT_AI_TEXT,
            },
            otherData: {
              infoList: this.currentData.aiList,
            },
            callback: async ({ data }) => {
              // 因为ai回复是异步的，而界面取缓存的时候直接使用已经经过ai回复的内容就行，不用再缓存ai组件
              this.paralegalTools.addReceiveData({
                type: "text",
                data: data.content,
              });

              this.refreshChatInfo(data);
              console.log(data, "callback data");
              this.isAiReply = false;
              resolve(data);
            },
          })
        );
      });
    },
    /**
     * 刷新聊天信息
     */
    refreshChatInfo(chatInfo) {
      // 如果是免费，并且次数为3，则清楚缓存
      if (!this.isPaid && chatInfo.remainTimes === 3) {
        clearSubmitAiHistory();
      }

      this.remainTimes = chatInfo.remainTimes;
      this.chatInfo = chatInfo;
    },
    /**
     * 登录之前发送消息
     */
    sendMsgBeforeLogin() {
      return new Promise(async (resolve) => {
        whetherToLogIn(async () => {
          const chatInfo = await this.getFreeChatTimes();
          this.refreshChatInfo(chatInfo);
          // 请求法律意见书
          await this.paySuccessCard();

          if (this.remainTimes <= 0) {
            await this.sendPayCard(chatInfo);
            resolve(false);
          } else {
            resolve(true);
          }
        });
      });
    },
    /**
     * 获取案源信息
     * 处理已有案源但是没有缓存的情况
     */
    async getCaseInfo() {
      if (!this.currentData.caseInfo.info) {
        const { data: caseSource } = await caseSourceV2GetCaseOrZx();
        if (Number(caseSource.type) !== 0) {
          this.currentData.caseInfo.info = caseSource.info;
        }
      }
    },
    /**
     * 发送消息
     */
    async sendMsg(info) {
      buryPointChannelBasics({
        code: "LAW_APPLET_AI_ASK_PAGE_SEND_BUTTON_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      try {
        if (!verifyLogin()) {
          const haveCount = await this.sendMsgBeforeLogin(info);

          if (!haveCount) {
            return;
          }
        }

        // 用户发送的消息
        await this.paralegalTools.delayAddData(this.currentData.newsList, [
          this.paralegalTools.addSendData({
            type: "text",
            data: info,
          }),
        ]);

        await this.getCaseInfo();

        this.currentData.caseInfo.info =
          (this.currentData.caseInfo.info || "") + info;

        // 这里是因为如果没有选择城市，那么流程会中断。直到选择城市后，才会继续执行
        // ! 这里要在调用 saveCaseInfoBefore 方法前赋值
        this.waitSaveCaseInfoCallback = async () => {
          console.log("触发了方法");
          const requestData = await this.sendMsgToAi(info);

          this.currentData.aiList.push({
            role: "assistant",
            info: requestData.content,
          });

          await this.sendPayCard(requestData);
        };

        console.log(
          this.currentData.caseInfo.info,
          "currentData.caseInfo.info"
        );
        await this.saveCaseInfoBefore();

        await this.waitSaveCaseInfoCallback();
      } catch (error) {
        console.log(error);
      }
    },
    handleVideoEnded() {
      this.isVideoEnded = true;
    },
  },
};
</script>
