import { requestCommon, requestCore, requestInfo } from "@/libs/axios";
import { storeRequest } from "@/libs/store-request";

/* 快速咨询用户购买服务滚动信息 2.1.0*/
export const fastConsultBuyUsers = data =>
  requestInfo.post("/lawyer/fastConsult/buyUsers", data);

/** 根据服务code 获取服务价格相关信息 */
export const serviceManegeInfo = (serverCode, lawyerId) =>
  requestCommon.post(`/info/serviceManege/info/${serverCode}`, { lawyerId });

/** 根据服务code 获取服务价格相关信息 */
export const serviceManegeInfoNew = (serverCode, data) =>
  requestCommon.post(`/info/serviceManege/info/${serverCode}`, data);

export function serviceManegeInfoCatch(data) {
  return storeRequest({
    api: ({ serviceCode, ...serviceData }) => serviceManegeInfoNew(serviceCode, serviceData),
    data,
    cacheName: "serviceManegeInfoCatch",
    storeMaxTime: 10000
  });
}

/* 获取系统参数*/
export const getCommonConfigKey = data =>
  requestCore.post("/common/config/key", data);

/* 案源创建*/
export const createOrder = data =>
  requestCommon.post("/order/v2/orderUser/createOrder", data, {
    loading: true
  });
/* 用户订单列表v2*/
export const ordersList = data =>
  requestCommon.post("/order/v2/orderUser/orders", data);

/* 支付*/
export const orderPay = data =>
  requestCommon.post("/order/v2/orderUser/pay", data);

/* 案源订单-退款申请接口*/
export const applyRefund = data =>
  requestCommon.post("/order/orderCaseSourceRefund/applyRefund", data);

/* 案源订单-退款信息查询*/
export const refundInfo = data =>
  requestCommon.post("/order/orderCaseSourceRefund/refundInfo", data);

/* 案源订单-退款时限查询*/
export const refundTime = data =>
  requestCommon.post("/order/orderCaseSourceRefund/refundTime", data);

/* 案源订单2.0.版本-退款申请接口*/
export const applyRefundNew = data =>
  requestCommon.post("/order/orderUserV2Refund/applyRefund", data);

/* 案源订单2.0.版本-退款信息查询*/
export const refundInfoNew = data =>
  requestCommon.post("/order/orderUserV2Refund/refundInfo", data);

/* 案源订单2.x.版本-退款申请接口*/
export const applyRefundByBusiness = data =>
  requestCommon.post("/order/orderUserV2Refund/applyRefundByBusiness", data);

/** 发起更换律师申请 */
export const userChangeLawyerApply = data =>
  requestCommon.post("/order/userChangeLawyerApply/apply", data);

/** 
 * 根据订单ID获取订单详情
 * https://showdoc.imlaw.cn/web/#/5/1758
 */
export const orderUserGetOrderById = data =>
  requestCommon.post("/order/v2/orderUser/getOrderById", data);

/** 更换律师详情（售后详情） */
export const userChangeLawyerApplyDetail = data =>
  requestCommon.post("/order/userChangeLawyerApply/detail", data);

/** 撤销换律师 */
export const userChangeLawyerApplyRevocation = data =>
  requestCommon.post("/order/userChangeLawyerApply/revocation", data);

/** 通过orderId获取服务信息 */
export const getServiceInfoByOrderId = data =>
  requestCommon.post("/order/orderUserV2Refund/getServiceInfoByOrderId", data);

/** 查询所有已配置的商品类型列表 */
export const indexPageServiceManagerInfoTypeValueList = data =>
  requestCommon.post("/info/indexPageServiceManagerInfo/typeValue/list", data);

/** 根据问题类型获取商品信息 */
export const indexPageServiceManagerInfoGetByTypeValue = data =>
  requestCommon.post("/info/indexPageServiceManagerInfo/getByTypeValue", data);

/** 获取多商品sku二级页面相关信息 */
export const indexPageServiceManagerInfoTypeValue = indexPageServiceManageInfoId =>
  requestCommon.post(
    `/info/indexPageServiceManagerInfo/typeValue/${indexPageServiceManageInfoId}`
  );

/** 查询最近一笔抖音通用交易支付系统支付成功并且用户未确认开始服务的订单 */
export const getLatestDouYinTradeOrder = () =>
  requestCommon.post("/order/v2/orderUser/getLatestDouYinTradeOrder");

/** 抖音通用交易支付用户确认开始服务 */
export const douYinTradeOrderConfirmStartService = data =>
  requestCommon.post(
    "/order/v2/orderUser/douYinTradeOrderConfirmStartService",
    data
  );


/** 合同下载是否新用户（剩余免费下载合同次数+7天内注册用户） */
export const getDownloadContractNewUser = data =>
  requestCommon.post(
    "/order/v2/orderUser/getDownloadContractNewUser",
    data
  );

/** 合同服务购买记录 小程序3.3.1版本新增 */
export const lawyerContractPayRecord = data =>
  requestCommon.post(
    "/info/lawyerContract/payRecord",
    data
  );


/** v2用户订单列表 */
export const orderUserOrdersList = data =>
  requestCommon.post(
    "/order/v2/orderUser/ordersList",
    data
  );