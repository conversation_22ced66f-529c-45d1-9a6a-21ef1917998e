<template>
  <div>
    <div v-if="show">
      <app-popup
        :show="show"
        :showCancelButton="false"
        :zIndex="2001"
      >
        <div
          class="w-[375px] box-border p-[16px_16px_16px_16px] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none position-relative"
        >
          <img
            alt=""
            class="absolute top-[12px] right-[12px] block w-[20px] h-[20px] z-10"
            src="@/components/telephone-consultation-popup/img/close.png"
            @click="closePopup"
          >
          <if t="hjls">
            <img
              alt=""
              class="background-image"
              src="@/pages/submit-question/findlawyer/imgs/<EMAIL>"
            >
          </if>
          <if f="hjls">
            <img
              alt=""
              class="background-image"
              src="@/pages/submit-question/findlawyer/imgs/<EMAIL>"
            >
          </if>
          <if t="hjls">
            <div class="font-bold text-[18px] text-[#222222]">
              AI智能匹配，平台优选律师<span class="text-[#F5883B]">免费解答</span>
            </div>
            <div class="text-[13px] text-[#666666] mt-[4px]">
              现在咨询，预计匹配3-5位律师回复
            </div>
          </if>
          <if f="hjls">
            <div class="font-bold text-[18px] text-[#222222]">
              如不满意律师服务，可<span class="text-[#F5883B]">免费</span>问其他律师
            </div>
            <div class="text-[13px] text-[#666666] mt-[4px]">
              补充越详细更容易匹配到越符合的专业律师哦
            </div>
          </if>
          <div>
            <div
              class="w-[343px] box-border px-[14px] bg-[#FFFFFF] rounded-[8px] mt-[20px]"
            >
              <div class="py-[12px]">
                <textarea
                  :showCount="false"
                  :value="params.info"
                  class="w-full h-[140px]"
                  style="font-size: 28rpx"
                  maxlength="300"
                  placeholder="请详细描述您想咨询的问题，包括发生的问题及时间，您的诉求，方便律师快速接入为您解答"
                  placeholderStyle="color: #999999;font-size: 28rpx;line-height: 32rpx;"
                  @focus="handleFocus"
                  @input="wordsCheck"
                />
                <!-- 底部的咨询类型按钮 -->
                <div class="mt-[16px] flex items-center justify-between">
                  <div class="text-[12px] text-[#999999]">
                    {{ params.info.length }}/300
                  </div>
                  <if t="hjls">
                    <div
                      class="p-[8px] bg-[#E5F3EE] rounded-[8px] border-[0.5px] border-solid border-[#A7D8C5] flex items-center justify-center box-border text-[13px] text-[#3887F5]"
                      @click="showPopup = true"
                    >
                      <img
                        v-if="!typeLabel"
                        alt=""
                        class="w-[12px] h-[12px] mr-[2px]"
                        src="@/pages/lawyer-home/img/11113.png"
                      >
                      <span>咨询类型<span v-if="typeLabel">：{{ typeLabel }}</span></span>
                    </div>
                  </if>
                  <if f="hjls">
                    <div
                      class="p-[8px] bg-[#EBF3FE] rounded-[8px] border-[0.5px] border-solid border-[#A0CFFB] flex items-center justify-center box-border text-[13px] text-[#3887F5]"
                      @click="showPopup = true"
                    >
                      <img
                        v-if="!typeLabel"
                        alt=""
                        class="w-[12px] h-[12px] mr-[2px]"
                        src="@/pages/lawyer-home/img/11113.png"
                      >
                      <span>咨询类型<span v-if="typeLabel">：{{ typeLabel }}</span></span>
                    </div>
                  </if>
                </div>
                <im-history
                  ref="history"
                  customStyle="padding: 0;background: #fff;margin-top: 24rpx;"
                  theme="white"
                  title="快速输入"
                  @history-select="historySelect"
                />
              </div>
              <div v-show="params.info">
                <app-form-item
                  :formLineStyle="{
                    borderBottom: '0.5px solid #eeeeee',
                    borderTop: '0.5px solid #eeeeee'
                  }"
                  label="涉案金额"
                >
                  <app-picker
                    v-model="params.caseAmount"
                    :dataSource="amountGrade"
                    titleText="请选择"
                  />
                </app-form-item>
                <app-form-item label="案件所在地">
                  <app-address
                    v-model="params.caseArea"
                    :labelStyle="{
                      fontWeight: '400',
                      color: '#CCCCCC',
                      fontSize: '15px'
                    }"
                    isDefaultCity
                  />
                </app-form-item>
              </div>
            </div>
          </div>
          <if t="hjls">
            <div
              :class="[isDisabled ? 'bg-[#A7D8C5]' : 'bg-[#50B08C]']"
              class="w-[343px] h-[42px] rounded-[52px] flex items-center justify-center font-bold text-[16px] text-[#FFFFFF] mt-[16px]"
              @click="publishDelegation"
            >
              免费咨询律师
            </div>
          </if>
          <if f="hjls">
            <div
              :class="[isDisabled ? 'bg-[#A0CFFB]' : 'bg-[#3887F5]']"
              class="w-[343px] h-[42px] rounded-[52px] flex items-center justify-center font-bold text-[16px] text-[#FFFFFF] mt-[16px]"
              @click="publishDelegation"
            >
              免费咨询律师
            </div>
          </if>
          <u-safe-bottom v-if="showSafeBottom" />
        </div>
      </app-popup>
      <app-popup-select
        :dataSource="lawyerSpeciality"
        :value="params"
        :visible.sync="showPopup"
        :zIndex="2010"
        @confirm="handleSelect"
      />
    </div>
    <retain-capital-popup-result v-model="resultPopup" />
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import AppPopupSelect from "@/components/app-components/app-form-list/app-popup-select/index.vue";
import AppAddress from "@/components/app-components/app-form-list/app-address/app-address.vue";
import AppFormItem from "@/components/app-components/app-form-list/app-form-item.vue";
import AppPicker from "@/components/app-components/app-form-list/app-picker.vue";
import {
  caseSourceV2GetInfo,
  caseSourceV2Save,
  consultationAliContent,
  dataDictionary,
  getCommonConfigKey
} from "@/api";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import ImHistory from "@/components/im-history/im-history.vue";
import { wordsCheckDoCheck } from "@/api/im";
import RetainCapitalPopupResult from "@/components/RetainCapitalPopupResult.vue";
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import { debounce, whetherToLogIn } from "@/libs/tools";
import { amountValue } from "@/pages/submit-question/components/card-consulting-options";

import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { saveParalegalData } from "@/libs/paralegalData";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";

export default {
  name: "RetainCapitalPopup",
  components: {
    USafeBottom,
    RetainCapitalPopupResult,
    ImHistory,
    AppPicker,
    AppFormItem,
    AppAddress,
    AppPopupSelect,
    AppPopup
  },
  props: {
    value: {
      type: Boolean,
      default: false,
      required: true
    },
    /** 默认文案 */
    defaultInfo: {
      type: String,
      default: ""
    },
    /** 底部安全距离 */
    showSafeBottom: {
      type: Boolean,
      default: true
    },
    /** 是否直接进入案源 */
    isCaseSource: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      /** 是否显示咨询类型弹窗 */
      showPopup: false,
      params: {
        info: "",
        typeLabel: "",
        typeValue: "",
        caseAmount: {},
        caseArea: {}
      },
      /** 涉及财产 */
      amountGrade: [],
      /** 案件类型 */
      lawyerSpeciality: [],
      /** 结果弹窗 */
      resultPopup: false,
      // 这里控制的是留资弹窗，如果有留资信息，则直接显示结果弹窗
      // 为了防止留资弹窗闪烁，所以引入了这个变量
      show: false,
      debounceAutoType: debounce(this.autoType, 500)
    };
  },
  computed: {
    typeLabel() {
      return this.params.typeLabel;
    },
    /** 按钮是否禁用 */
    isDisabled() {
      if (this.params.info.length < 10) {
        return true;
      }

      if (!this.params.typeValue) {
        return true;
      }

      if (!this.params.caseAmount.typeValue) {
        return true;
      }

      if (!this.params.caseArea.cityCode) {
        return true;
      }

      return false;
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.getData();

          caseSourceV2GetCaseOrZx()
            .then(({ data }) => {
              console.log(data, "data");
              // 如果已经有了留资信息，则直接展示结果
              if (Number(data.type) !== 0) {
                this.closePopup();
                this.resultPopup = true;
              } else {
                buryPointTransformationPath.new(5038);

                buryPointChannelBasics({
                  code: "LAW_APPLET_AFTER_PAY_SECOND_LEAVE_INFO_POPUP_PAGE",
                  behavior: BURY_POINT_CHANNEL_TYPE.VI,
                  type: 1
                });

                buryPointChannelBasics({
                  code:
                    "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_LEFT_INFO_POP_UP_PAGE",
                  type: 1,
                  behavior: BURY_POINT_CHANNEL_TYPE.CK
                });
                // 赋值默认文案
                this.params.info = this.defaultInfo;
                this.show = true;
                this.$refs.history.getHistoryData();
              }
            })
            .catch(res => {
              buryPointChannelBasics({
                code:
                  "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_LEFT_INFO_POP_UP_PAGE",
                type: 1,
                behavior: BURY_POINT_CHANNEL_TYPE.CK
              });
              // 如果是没有登录的情况
              console.log(res);
              this.show = true;
            });
        }
      },
      immediate: true
    },
    "params.info": {
      handler(val) {
        if (val) this.debounceAutoType();
      }
    }
  },
  methods: {
    /** 验证参数 */
    validateParams() {
      // 判断是否是 10-300 字
      if (this.params.info.length < 10 || this.params.info.length > 300) {
        uni.showToast({
          title: "请填写10-300字的问题描述",
          icon: "none"
        });
        return false;
      }

      return true;
    },
    /** 关闭留资弹窗 */
    closePopup() {
      this.show = false;
      this.$emit("input", false);
    },
    /** 通过留资自动判断类型 */
    async autoType() {
      const res = await consultationAliContent({
        description: this.params.info
      });

      this.params.typeValue = res.data.typeValue;
      this.params.typeLabel = res.data.typeLabel;
    },
    /** 获取案件类型数据 */
    getData() {
      /* 获取 涉及财产 选择数据 */
      dataDictionary({ groupCode: "AMOUNT_GRADE" })?.then(({ data }) => {
        this.amountGrade = data;

        console.log(this.amountGrade, "this.amountGrade");

        // * 默认选择 面议
        const defaultCaseAmount = this.amountGrade.find(
          item => Number(item.value) === amountValue.NEGOTIATION
        );

        if (defaultCaseAmount) {
          this.params.caseAmount = {
            typeLabel: defaultCaseAmount.label,
            typeValue: defaultCaseAmount.value
          };
        }
      });

      /** 案件类型 */
      dataDictionary({
        groupCode: "LAWYER_SPECIALITY"
      }).then(lawyerSpeciality => {
        lawyerSpeciality.data.forEach(item => {
          item.value = Number(item.value);

          // 默认综合咨询
          if (item.value === 26) {
            this.params.typeValue = item.value;
            this.params.typeLabel = item.label;
          }
        });

        this.lawyerSpeciality = lawyerSpeciality.data;
      });
    },
    handleFocus() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_AFTER_PAY_SECOND_LEAVE_INFO_POPUP_PAGE_INPUT_BOX_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      buryPointChannelBasics({
        code:
          "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_LEFT_INFO_POP_UP_PAGE_INPUT_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });
    },
    wordsCheck(e) {
      console.log(e);

      // 限定300个字符
      if (e.target.value.length > 300) {
        this.params.info = e.target.value.slice(0, 300);
        return;
      }

      this.params.info = e.target.value;
    },
    handleSelect(item) {
      this.params = {
        ...this.params,
        ...item
      };
    },
    /** 选择历史记录后触发的事件 */
    historySelect(value) {
      console.log("@history-select:", value);
      // this.saveAppointParams.info = value.info;
      this.params.info = value;
    },
    /** 发布委托 */
    publishDelegation() {
      if (!this.validateParams() || this.isDisabled) {
        return;
      }

      buryPointChannelBasics({
        code:
          "LAW_APPLET_AFTER_PAY_SECOND_LEAVE_INFO_POPUP_PAGE_FREE_ASK_OTHER_LAWYER_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      buryPointChannelBasics({
        code:
          "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_LEFT_INFO_POP_UP_PAGE_FREE_CONSULT_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      whetherToLogIn(async () => {
        try {
          console.log(this.params);

          this.params.info = this.params.info.trim();

          await wordsCheckDoCheck({
            text: this.params.info
          });

          // 保存信息
          const res = await caseSourceV2GetInfo();

          // 如果 res.data 有值，则不生成案源
          if (res.data) {
            this.closePopup();
            this.resultPopup = true;
            return;
          }

          const { data } = await getCommonConfigKey({
            paramName: "law_pay_case_source_quickly_default_service_code"
          });

          const params = {
            info: this.params.info,
            typeLabel: this.params.typeLabel,
            typeValue: this.params.typeValue,
            type: 1
          };

          // 保存历史消息
          await saveParalegalData(params);

          await caseSourceV2Save({
            info: this.params?.info,
            typeLabel: this.params?.typeLabel,
            typeValue: this.params?.typeValue,
            amountGrade: this.params?.caseAmount.typeValue,
            happenAddress:
              (this.params?.caseArea?.provinceName || "") +
              (this.params?.caseArea?.cityName || ""),
            regionCode: this.params?.caseArea?.cityCode,
            squareShow: 1,
            isDistributed: false,
            serverCode: data.paramValue,
            directToCaseSource: this.isCaseSource
          });

          this.closePopup();
          this.resultPopup = true;

          buryPointChannelBasics({
            code:
              "LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE_LEAVE_INFO_SUCCESS",
            behavior: BURY_POINT_CHANNEL_TYPE.VI,
            type: 1
          });
        } catch (e) {
          console.log(e);
        }
      });
    }
  }
};
</script>
