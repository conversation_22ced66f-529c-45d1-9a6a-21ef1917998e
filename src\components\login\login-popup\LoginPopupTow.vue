<template>
  <div>
    <app-popup
      :closeOnClickOverlay="false"
      :duration="0"
      :round="16"
      :safeAreaInsetBottom="false"
      :show="show"
      :zIndex="zIndex"
      mode="bottom"
      @open="open"
    >
      <div class="p-[24px_24px_16px_24px]">
        <div class="flex items-center justify-between">
          <img
            alt=""
            class="w-[150px] h-[36px] block"
            src="@/components/login/imgs/s75hRY.png"
          >
          <img
            alt=""
            class="w-[24px] h-[24px] block"
            src="@/components/login/imgs/pO9XJ6.png"
            @click="loginPopupClose"
          >
        </div>
        <!-- #ifdef MP-WEIXIN -->
        <div class="font-bold text-[16px] text-[#333333] mt-[24px]">
          申请使用获取您微信绑定的手机号
        </div>
        <div class="flex items-center mt-[6px]">
          <button
            class="text-[14px] text-[#333333] bg-transparent"
            open-type="getPhoneNumber"
            @click="btnHandleClick(false, true)"
            @getphonenumber="getPhoneNumberHandler"
          >
            使用其他手机号授权
          </button>
        </div>
        <!-- #endif -->
        <div class="text-[13px] text-[#999999] flex items-center mt-[16px]">
          <img
            v-if="check"
            alt=""
            class="w-[16px] h-[16px] block"
            src="../imgs/<EMAIL>"
            @click="check = false"
          >
          <img
            v-else
            alt=""
            class="w-[16px] h-[16px] block"
            src="../imgs/check.png"
            @click="check = true"
          >
          <span class="ml-[4px]">
            阅读并同意
          </span>
          <span
            class="text-[#39C49F]"
            @click="protocolClick(1)"
          >
            《隐私政策》
          </span>
          <span
            class="text-[#39C49F]"
            @click="protocolClick(2)"
          >
            《用户服务协议》
          </span>
        </div>
        <button
          class="mt-[14px] w-[327px] h-[44px] bg-[#39C49F] rounded-[68px] font-bold text-[16px] text-[#FFFFFF] flex items-center justify-center"
          open-type="getPhoneNumber"
          @click="btnHandleClick(false, true)"
          @getphonenumber="getPhoneNumberHandler"
        >
          同意授权
        </button>
      </div>
      <u-safe-bottom />
    </app-popup>
  </div>
</template>
<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import LoginPopupMixin from "@/components/login/mixins/LoginPopupMixin";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "LoginPopupTow",
  components: { USafeBottom, AppPopup },
  mixins: [LoginPopupMixin]
};
</script>
