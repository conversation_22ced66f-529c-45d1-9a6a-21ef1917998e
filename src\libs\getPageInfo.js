import { getChannelPageInfo } from "@/api";
import { serviceManegeInfoCatch } from "@/api/order";
import { specialUnStreamLeftInfoCategory } from "@/api/special";
import { caseSourceDetermineHighValueByCaseType } from "@/api/user";
import { isPlatformIn, PLATFORM_TYPES } from "@/libs/appConfig";
import { isNull, isObj, isObjNull } from "@/libs/basics-tools";
import { storeRequest } from "@/libs/store-request";
import { jsonParse } from "@/libs/tools";
import { verifyLogin } from "./login";
import dayjs from "dayjs";

export const PAGE_MARK_ENUM = {
  SUBMIT_QUESTION: isPlatformIn(PLATFORM_TYPES.SHALLOW_MAJIA) ? "SubmitQuestionShallow" : "SubmitQuestion",
  SUBMIT_QUESTION_AI: "SubmitQuestionAi",
};

export const POSITION_ENUM = {
  /** 小程序顶部支付卡片 */
  TOP_PAY_CARD: "TOP_PAY_CARD",
  /** 小程序律师假卡片 */
  LAWYER_CARD: "LAWYER_CARD",
  /** 小程序底部公共咨询卡片 */
  BOTTOM_CONSULT_CARD: "BOTTOM_CONSULT_CARD",
  /** 小程序诉讼指导卡片 */
  LITIGATION_GUIDANCE_CARD: "LITIGATION_GUIDANCE_CARD",
  /** 小程序停留弹窗权益升级卡片 */
  UPGRADE_CARD: "UPGRADE_CARD",
  /** 小程序停留弹窗解决方案卡片 */
  SOLUTION_CARD: "SOLUTION_CARD",
  /** 法律意见书卡片 */
  LEGAL_OPINION: "LEGAL_OPINION",
  /** ai畅聊卡片 */
  AI_CHAT: "AI_CHAT",
  /** 底部支付卡片 */
  BOTTOM: "bottom",
  /** 高诉讼案件卡片 */
  HIGH_LITIGATION: "highLitigation",
  /** 法律意见书弹窗 */
  LEGAL_OPINION_POPUP: "LEGAL_OPINION_POPUP",
  /** 法律意见书已生成弹窗 */
  LEGAL_OPINION_GENERATED_POPUP: "LEGAL_OPINION_GENERATED_POPUP",
};

/**
 * 获取服务配置
 * https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=69b91496-d9db-47df-ad35-2a14c22b5342&versionId=d56690a8-bfc4-4775-8f8e-3286a5926bc8&docId=126b0a21-7d3a-4ca1-88ea-faf4c7279e76&docType=axure&pageId=038aa9e674e94709ba37fd79393870cd&image_id=126b0a21-7d3a-4ca1-88ea-faf4c7279e76&parentId=cf544442-f056-4923-909f-3d34963dbcf8
 * @return Promise<object>
 */
export function getChannelServiceInfo({ pageMark = PAGE_MARK_ENUM.SUBMIT_QUESTION } = {}) {
  if (!pageMark) {
    return Promise.reject("no pageMark");
  }

  return getChannelPageInfo({ pageMark }).then(({ data = {} }) => {
    if (isNull(data) || isObjNull(data)) return Promise.reject("no data");

    let priceReductionConfig = data.priceReductionConfig || "";

    if (isNull(priceReductionConfig))
      return Promise.reject("no priceReductionConfig");

    const parseObj = {
      priceReductionConfig: jsonParse(priceReductionConfig),
      extra: jsonParse(data.extra || ""),
    };

    return uni.$u.deepMerge(data, parseObj) || {};
  });
}

/** 获取信息缓存 */
export function getChannelServiceInfoCatch(data) {
  return storeRequest({
    api: getChannelServiceInfo,
    data,
    cacheName: "getChannelServiceInfo",
    storeMaxTime: 3000,
  });
}

/**
 * 判断是否是高价值用户
 * @param data
 * @returns {Promise<Object>}
 */
export function getHighValueUserInfo(data) {
  // 如果未登录，则返回false
  if(!verifyLogin()) return Promise.resolve({
    highValue: false,
  });

  return caseSourceDetermineHighValueByCaseType(data).then(({ data = {} }) => {
    const { highValue, content = [] } = data;

    if (highValue === 1) {
      return {
        highValue: true,
      };
    }

    if (content?.length > 0) {
      return {
        highValue: true,
      };
    }

    return {
      highValue: false,
    };
  });
}

export function getHighValueUserInfoCatch(data) {
  return storeRequest({
    api: getHighValueUserInfo,
    data,
    cacheName: "getHighValueUserInfo",
    storeMaxTime: 10000,
  });
}

/**
 * 获取支付卡片位置信息
 * @param position 如果对应位置存在，返回得是对应的服务信息，否则返回所有有所位置得服务信息
 * @param pageMark
 * @returns {Promise<Object>}
 */
export function getServicePositionPayInfo({
  position,
  pageMark,
} = {}) {
  return getChannelServiceInfoCatch({ pageMark }).then(async(data) => {
    const { priceReductionConfig } = data;

    if (!isObj(priceReductionConfig)) return Promise.reject();

    if (isObjNull(priceReductionConfig)) return Promise.reject();

    if (priceReductionConfig[position]) {
      let serviceCode = priceReductionConfig[position].serviceCode;

      const { highValue } = await getHighValueUserInfoCatch({
        key: "DESCRIBE_THE_HIT_KW",
      });

      if (highValue) {
        serviceCode = priceReductionConfig[position].high || serviceCode;
      }

      const needProductPriceCoefficientKey = priceReductionConfig[position]?.needProductPriceCoefficientKey || 0;
      
      // 如果 needProductPriceCoefficientKey 为 1，需要等待 specialUnStreamLeftInfoCategory 请求完成
      if (Number(needProductPriceCoefficientKey) === 1) {
        // 因为后端那边需要这个接口请求过才能根据案源信息修改价格，所以这里直接先请求一次

        return specialUnStreamLeftInfoCategory().then(() => {
          return serviceManegeInfoCatch({ serviceCode, needProductPriceCoefficientKey }).then(({ data = {} }) => {
            return data;
          });
        });
      }

      return serviceManegeInfoCatch({ serviceCode, needProductPriceCoefficientKey }).then(({ data = {} }) => {
        return data;
      });
    }

    return priceReductionConfig;
  });
}

/**
 * 获取对应extra
 */
export function getChannelServiceInfoExtra({ extraKey, ...data } = {}) {
  return getChannelServiceInfoCatch(data).then((data) => {
    if (data.extra && isObj(data.extra)) {
      const extra = data.extra;
      /* 判断当前降价弹窗是配置的哪一个 */
      if (extraKey)
        return !isNull(extra[extraKey])
          ? Promise.resolve(extra[extraKey])
          : Promise.reject(`extra no have extraKey ${extraKey}`);
      else return Promise.resolve(extra);
    }

    return Promise.reject("no extra");
  });
}
