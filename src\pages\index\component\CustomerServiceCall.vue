<template>
  <div
    class="mt-[12px]"
  >
    <div
      class="w-[88px] h-[29px] bg-[#3887F5] rounded-[40px] flex items-center justify-center box-border mx-auto"  
      @click="callCustomerService"
    >
      <div class="text-[12px] text-[#FFFFFF]">
        客服热线
      </div>
    </div>
    <div class="text-[12px] text-[rgba(153,153,153,0.6)] mt-[10px] text-center">
      服务时间：工作日{{ customerServiceInfo.serviceTime }}
    </div>
  </div>
</template>

<script>
import { getCommonConfigKey } from "@/api/order";

export default {
  name: "CustomerServiceCall",
  data() {
    return {
      customerServiceInfo: {}
    };
  },
  mounted() {
    this.getCustomerServiceInfo();
  },
  methods: {
    getCustomerServiceInfo() {
      getCommonConfigKey({
        paramName: "CUSTOM_USER_SERVICE_INFO"
      }).then(({ data }) => {
        try {
          this.customerServiceInfo = JSON.parse(data.paramValue);
        } catch (e) {
          this.customerServiceInfo = {};
        }
      });
    },
    callCustomerService() {
      uni.makePhoneCall({
        phoneNumber: this.customerServiceInfo.phone,
      });
    }
  }
};
</script>