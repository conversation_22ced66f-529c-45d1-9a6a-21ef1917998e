<template>
  <app-popup
    :show="show"
    :closeOnClickOverlay="false"
    showCancelButton
    zIndex="9991"
    @cancel="close"
    @close="close"
  >
    1
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import AiPayMixin from "@/pages/submit-question/ai/AiPayMixin.js";
import { PAGE_MARK_ENUM, POSITION_ENUM } from "@/libs/getPageInfo";

export default {
  name: "LegalOpinionPopup",
  components: {
    AppPopup,
  },
  mixins: [AiPayMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    caseDescription: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      show: false,
      pageMark: PAGE_MARK_ENUM.SUBMIT_QUESTION_AI,
      position: POSITION_ENUM.LEGAL_OPINION_POPUP,
      buryPointPath: 5060,
      buryPointCode: "LAW_APPLET_AI_LEGAL_OPINION_POPUP_PAY_CLICK",
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  methods: {
    close() {
      this.show = false;
      this.$emit("close");
    },
    handlePay() {
      this.pay();
    }
  },
};
</script>