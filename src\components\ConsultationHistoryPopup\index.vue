<template>
  <app-popup
    :closeOnClickOverlay="false"
    :showCancelButton="showCancelButton"
    :show="show"
    mode="bottom"
    :zIndex="9995"
    @cancel="handleCancel"
  >
    <div class="px-[12px] pt-[20px] pb-[16px]">
      <div class="flex items-center">
        <img
          class="block w-[18px] h-[18px]"
          alt=""
          src="@/components/ConsultationHistoryPopup/img/<EMAIL>"
        >
        <div class="font-bold text-[14px] text-[#333333] ml-[4px]">
          我的历史咨询
        </div>
      </div>
      <div class="bg-[#F5F5F7] rounded-[8px] p-[12px] box-border mt-[12px] h-[72px]">
        <div
          v-if="consultationHistory.info"
          class="text-[14px] text-[#333333] text-ellipsis-2"
        >
          {{ consultationHistory.info }}
        </div>
        <div
          v-else
          class="text-[14px] text-[#999999]"
        >
          您的咨询信息已过期，可点击下方按钮再次咨询
        </div>
      </div>
      <div class="flex items-center mt-[16px]">
        <img
          class="block w-[18px] h-[18px]"
          alt=""
          src="@/components/ConsultationHistoryPopup/img/<EMAIL>"
        >
        <div class="font-bold text-[14px] text-[#333333] ml-[4px]">
          以下本地律师在线，可继续邀请律师解答
        </div>
      </div>
      <img
        class="block w-[351px] h-[135px] mt-[8px]"
        alt=""
        src="@/components/ConsultationHistoryPopup/img/<EMAIL>"
        @click="navigateToAskLawyerPage"
      >
      <div
        class="w-[351px] h-[44px] bg-[#3887F5] rounded-[68px] position-relative box-border flex items-center justify-center mt-[16px]"
        @click="handleConsultAgain"
      >
        <div
          class="absolute -top-[8px] right-[96px] px-[8px] py-[2px] box-border h-[20px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-tl-[10px] rounded-br-[10px] rounded-tr-[10px] rounded-bl-[2px]"
        >
          <div class="font-bold text-[12px] text-[#FFFFFF]">
            免费
          </div>
        </div>
        <div class="font-bold text-[16px] text-[#FFFFFF]">
          再次咨询
        </div>
      </div>
    </div>
    <u-safe-bottom />
  </app-popup>
</template>

<script>
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { toAskLawyer, toConsultation } from "@/libs/turnPages.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { getConsultationHistoryCache, clearConsultationHistoryCache } from "./index.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";

export default {
  name: "ConsultationHistoryPopup",
  components: { AppPopup, USafeBottom },
  data() {
    return {
      consultationHistory: {},
      show: false,
      showCancelButton: false
    };
  },
  mounted() {
    this.checkCacheAndShowPopup();
  },
  methods: {
    /**
     * 检查缓存并显示弹窗
     */
    checkCacheAndShowPopup() {
      const cachedData = getConsultationHistoryCache();
      if (cachedData) {
        buryPointChannelBasics({
          code:
          "LAW_APPLET_INDEX_PAGE_CONSULT_AGAIN_POPUP_PAGE",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI
        });

        this.show = true;
        this.queryConsultationHistory();

        setTimeout(() => {
          this.showCancelButton = true;
        }, 2000);

        clearConsultationHistoryCache();
        console.log("检测到咨询历史缓存，显示弹窗并清除缓存");
      }
    },
    /**
     * 案源查询 - 查询咨询历史
     */
    async queryConsultationHistory() {
      try {
        const response = await caseSourceV2GetCaseOrZx();
        this.consultationHistory = response.data;
        console.log("咨询历史数据:", this.consultationHistory);
      } catch (error) {
        console.error("获取咨询历史失败:", error);
        this.consultationHistory = null;
      }
    },
    /**
     * 当没有咨询历史时点击律师卡片导航到"问律师"页面
     */
    navigateToAskLawyerPage() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_INDEX_PAGE_CONSULT_AGAIN_POPUP_LAWYER_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      // 如果没有咨询历史，点击律师卡片跳转到问律师页面
      if (!this.consultationHistory || !this.consultationHistory.info) {
        toAskLawyer();
        this.show = false;
      }
    },

    /**
     * 处理再次咨询按钮点击
     */
    handleConsultAgain() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_INDEX_PAGE_CONSULT_AGAIN_POPUP_CONSULT_BUTTON_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      if(this.consultationHistory.info) {
        const type = Number(this.consultationHistory.type);
        
        if(type === 1) {
          toConsultation({ index: 2 });
        }

        if(type === 2) {
          toConsultation({ index: 0 });
        }
        this.show = false;
      } else {
        toAskLawyer();
        this.show = false;
      }
    },
    handleCancel() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_INDEX_PAGE_CONSULT_AGAIN_POPUP_CLOSE_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      this.show = false;
    }
  },
};
</script>
