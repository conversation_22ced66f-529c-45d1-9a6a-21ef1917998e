<template>
  <div
    class="fixed absolute-center-y"
    :style="{ top: position.y + 'px', right: position.x + 'px' }"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  >
    <img
      class="block w-[32px] h-[32px]"
      alt=""
      src="@/pages/submit-question/imgs/Frame1321315563.png"
      @click="handleGoTo"
    >
  </div>
</template>

<script>
import { toWeChatCustomerService } from "@/libs/tools";

export default {
  name: "WeChatCustomerService",
  data() {
    return {
      position: {
        x: 8, // 距离右边的距离
        y: 0  // 距离顶部的距离，初始化为屏幕中间位置
      },
      isDragging: false,
      startPosition: {
        x: 0,
        y: 0
      },
      startTouch: {
        x: 0,
        y: 0
      },
      moveThreshold: 10, // 移动阈值，超过这个距离认为是拖拽而非点击
      hasMoved: false // 标记是否有移动
    };
  },
  computed: {
    tabsHeight() {
      return this.$store.getters.getTabsHeight;
    },
  },
  mounted() {
    // 初始化位置为屏幕中间
    this.initPosition();
  },
  methods: {
    initPosition() {
      // 获取屏幕信息来设置初始位置
      const systemInfo = uni.getSystemInfoSync();
      this.position.y = systemInfo.windowHeight / 2 - 16; // 减去图标高度的一半
    },

    handleTouchStart(e) {
      this.isDragging = false;
      this.hasMoved = false;

      // 记录开始位置
      this.startPosition.x = this.position.x;
      this.startPosition.y = this.position.y;

      // 记录触摸开始位置
      this.startTouch.x = e.touches[0].clientX;
      this.startTouch.y = e.touches[0].clientY;
    },

    handleTouchMove(e) {
      // 计算移动距离
      const deltaX = Math.abs(e.touches[0].clientX - this.startTouch.x);
      const deltaY = Math.abs(e.touches[0].clientY - this.startTouch.y);

      // 如果移动距离超过阈值，标记为拖拽
      if (deltaX > this.moveThreshold || deltaY > this.moveThreshold) {
        this.isDragging = true;
        this.hasMoved = true;
        
        // 只有在拖拽时才阻止默认行为
        e.preventDefault();
      }

      if (!this.isDragging) return;

      // 计算移动距离
      const moveX = this.startTouch.x - e.touches[0].clientX;
      const moveY = e.touches[0].clientY - this.startTouch.y;

      // 更新位置
      this.position.x = this.startPosition.x + moveX;
      this.position.y = this.startPosition.y + moveY;

      // 边界检查
      this.checkBoundaries();
    },

    handleTouchEnd(e) {
      // 如果发生了拖拽，进行边缘吸附
      if (this.isDragging) {
        // 阻止可能的点击事件
        e.preventDefault();
        
        // 自动吸附到屏幕边缘
        this.snapToEdge();
      }
      
      // 重置拖拽状态
      this.isDragging = false;
      
      // 延迟重置 hasMoved 状态，确保 click 事件能正确判断
      setTimeout(() => {
        this.hasMoved = false;
      }, 10);
    },

    checkBoundaries() {
      const systemInfo = uni.getSystemInfoSync();
      const iconSize = 32;

      // 限制在屏幕范围内
      if (this.position.x < 0) {
        this.position.x = 0;
      }
      if (this.position.x > systemInfo.windowWidth - iconSize) {
        this.position.x = systemInfo.windowWidth - iconSize;
      }
      if (this.position.y < 88) {
        this.position.y = 88;
      }
      if (this.position.y > systemInfo.windowHeight - 88 - this.tabsHeight) {
        this.position.y = systemInfo.windowHeight - 88 - this.tabsHeight;
      }
    },

    snapToEdge() {
      const systemInfo = uni.getSystemInfoSync();
      const iconSize = 32;
      const currentX = systemInfo.windowWidth - this.position.x - iconSize; // 计算距离左边的实际位置
      const screenCenter = systemInfo.windowWidth / 2;

      // 自动吸附到左右边缘
      if (currentX < screenCenter) {
        // 吸附到左边
        this.position.x = systemInfo.windowWidth - iconSize - 8; // right = screenWidth - iconSize - margin
      } else {
        // 吸附到右边
        this.position.x = 8; // right = 8
      }
    },

    handleGoTo() {
      // 只有在没有拖拽移动的情况下才响应点击
      if (!this.hasMoved) {
        toWeChatCustomerService();
      }
    }
  }
};
</script>

<style scoped>
.fixed {
  touch-action: none;
  z-index: 999;
}
</style>
