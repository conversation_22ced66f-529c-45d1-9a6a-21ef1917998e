<template>
  <div v-if="show">
    <div class="tabs w-[375px] fixed bottom-0 left-0 box-border z-[9000]">
      <div
        :style="{
          gridTemplateColumns: `repeat(${list.length}, 1fr)`,
        }"
        class="w-[375px] h-[48px] position-relative grid bg-[#FFFFFF]"
      >
        <div
          v-for="item in list"
          :key="item.pagePath"
        >
          <div
            class="w-full h-full relative flex items-center justify-center flex-col"
            @click="turnToPage(item.pagePath)"
          >
            <div v-if="item.pagePath === fastAskPath()">
              <div class="bottom-[5px] absolute absolute-center-x">
                <img
                  src="@/components/app-components/AppCustomTabBar/img/middle.gif"
                  class="block w-[60px] h-[55px]"
                  alt=""
                >
                <div
                  class="text-[10px] text-[#999999] mt-[6px] text-center font-[700]"
                >
                  <!-- #ifndef MP-TOUTIAO -->
                  AI问律师
                  <!-- #endif -->
                  <!-- #ifdef MP-TOUTIAO -->
                  极速问律师
                  <!-- #endif -->
                </div>
              </div>
            </div>
            <div
              v-else
              class="flex flex-col items-center justify-center"
            >
              <div class="position-relative">
                <div
                  v-if="
                    item.pagePath === toConsultationPage({ index: 1 }) &&
                      messageCount > 0
                  "
                  class="w-[10px] h-[10px] bg-[#F34747] border-[1px] border-solid border-[#FFFFFF] rounded-full absolute top-0 right-[1px] box-border"
                />
                <img
                  :src="
                    selected(item.pagePath)
                      ? item.selectedIconPath
                      : item.iconPath
                  "
                  alt=""
                  class="w-[24px] h-[24px] block"
                >
              </div>
              <div
                :class="[
                  selected(item.pagePath) ? 'text-[#3887F5]' : 'text-[#999999]',
                ]"
                class="font-bold text-[10px] text-[#3887F5] mt-[2px] text-center"
              >
                {{ item.text }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <u-safe-bottom customStyle="background-color: #FFFFFF;" />
    </div>
    <app-placeholder
      :height="tabsHeight"
      :showSafe="false"
    />
  </div>
</template>

<script>
import AppPlaceholder from "@/components/app-components/app-placeholder/index.vue";
import { getClientRect } from "@/libs/tools";
import {
  fastAskPath, findLawyerPath,
  getCurrentPageRoute,
  hotTopicPath,
  toConsultation,
  toConsultationPage,
  toFastAskPage, toFindLawyer,
  toHotTopic,
} from "@/libs/turnPages";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { wdOrZxInfo } from "@/api/user";
import { bindOnHook } from "@/libs/hooks";

export default {
  name: "AppCustomTabBar",
  components: { USafeBottom, AppPlaceholder },
  data() {
    return {
      list: [
        {
          pagePath: "/pages/index/index",
          iconPath: require("@/assets/imgs/tabBar/home.png"),
          selectedIconPath: require("@/assets/imgs/tabBar/home-active.png"),
          text: "首页",
        },
        {
          pagePath: findLawyerPath(),
          iconPath: require("@/assets/imgs/tabBar/findlawyer.png"),
          selectedIconPath: require("@/assets/imgs/tabBar/findlawyer-active.png"),
          text: "找律师",
        },
        {
          pagePath: fastAskPath(),
          text: "问",
        },
        {
          pagePath: toConsultationPage({ index: 1 }),
          iconPath: require("@/assets/imgs/tabBar/jk7lii.png"),
          selectedIconPath: require("@/assets/imgs/tabBar/x92up9.png"),
          text: "消息",
        },
        {
          pagePath: "/pages/mine/index",
          iconPath: require("@/assets/imgs/tabBar/mine.png"),
          selectedIconPath: require("@/assets/imgs/tabBar/mine-active.png"),
          text: "我的",
        },
      ],
      show: false,
      currentPage: "",
      messageCount: 0,
    };
  },
  computed: {
    tabsHeight() {
      return this.$store.getters.getTabsHeight;
    },
  },
  watch: {
    show: {
      handler(value) {
        if (value) {
          this.$nextTick(() => {
            this.getTabsHeight();
          });
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // #ifdef MP-BAIDU
    this.$nextTick(() => {
      // #endif
      this.canShow();
      // #ifdef MP-BAIDU
    });
    // #endif
    this.getNoEvaluated();

    bindOnHook.call(this, "onShow", this.getNoEvaluated);
  },
  methods: {
    toConsultationPage,
    /** 获取数据 */
    getNoEvaluated() {
      if (this.isLogin) {
        wdOrZxInfo().then(({ data }) => {
          this.messageCount = data.caseNum + data.payConsultNum;
        });
      }
    },
    fastAskPath,
    /** 判断是否展示 */
    canShow() {
      const currentPage = getCurrentPageRoute();

      this.currentPage = currentPage.fullPath;

      // 处理特殊的没有在列表中的路由
      const specialPage = [toConsultationPage({ index: 2 })];

      this.show = this.list.some((item) => {
        if (specialPage.includes(currentPage.fullPath)) {
          return true;
        }

        return (
          currentPage.fullPath.includes(item.pagePath) &&
          item.pagePath !== fastAskPath()
        );
      });
    },
    turnToPage(pagePath) {
      if (pagePath === hotTopicPath()) {
        toHotTopic();

        return;
      } else if (pagePath === fastAskPath()) {
        toFastAskPage();
        return;
      } else if (pagePath === toConsultationPage({ index: 1 })) {
        toConsultation({ index: 1 }, true);
        return;
      } else if (pagePath === findLawyerPath()) {
        toFindLawyer();
      }

      uni.switchTab({
        url: pagePath,
      });
    },
    selected(pagePath) {
      // 特殊处理路由
      if (pagePath === toConsultationPage({ index: 1 })) {
        return (
          this.currentPage === toConsultationPage({ index: 1 }) ||
          this.currentPage === toConsultationPage({ index: 2 })
        );
      }

      return this.currentPage.includes(pagePath);
    },
    /** 获取tabs的高度 */
    getTabsHeight() {
      getClientRect.call(this, ".tabs").then((rect) => {
        // #ifndef MP-TOUTIAO
        this.$store.commit("SET_TABS_HEIGHT", rect.height);
        // #endif
        // #ifdef MP-TOUTIAO
        this.$store.commit("SET_TABS_HEIGHT", rect.height + uni.$u.sys().safeAreaInsets.bottom);
        // #endif
      });
    },
  },
};
</script>
