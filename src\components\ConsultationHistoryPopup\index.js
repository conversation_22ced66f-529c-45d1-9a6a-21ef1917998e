/**
 * 咨询历史弹窗缓存管理
 */

const CONSULTATION_HISTORY_CACHE_KEY = "consultation_history_popup_cache";

/**
 * 存储咨询历史弹窗缓存数据
 * @param {Object} data 要缓存的数据
 */
export function setConsultationHistoryCache(data) {
  try {
    uni.setStorageSync(CONSULTATION_HISTORY_CACHE_KEY, data);
    console.log("咨询历史弹窗缓存已存储");
  } catch (error) {
    console.error("存储咨询历史弹窗缓存失败:", error);
  }
}

/**
 * 获取咨询历史弹窗缓存数据
 * @returns {Object|null} 缓存的数据，如果没有或解析失败则返回null
 */
export function getConsultationHistoryCache() {
  try {
    return uni.getStorageSync(CONSULTATION_HISTORY_CACHE_KEY);
  } catch (error) {
    console.error("获取咨询历史弹窗缓存失败:", error);
    return null;
  }
}

/**
 * 清除咨询历史弹窗缓存数据
 */
export function clearConsultationHistoryCache() {
  try {
    uni.removeStorageSync(CONSULTATION_HISTORY_CACHE_KEY);
    console.log("咨询历史弹窗缓存已清除");
  } catch (error) {
    console.error("清除咨询历史弹窗缓存失败:", error);
  }
}